//
// Generated by LLVM NVPTX Back-End
//

.version 7.5
.target sm_50
.address_size 64

	// .globl	_Z13MatrixMulCUDAILi16EEvPfS0_S0_ii
.global .align 1 .b8 blockIdx[1];
.global .align 1 .b8 threadIdx[1];
.weak .shared .align 4 .b8 _ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2As[1024];
.weak .shared .align 4 .b8 _ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2Bs[1024];
.weak .shared .align 4 .b8 _ZZ13MatrixMulCUDAILi32EEvPfS0_S0_iiE2As[4096];
.weak .shared .align 4 .b8 _ZZ13MatrixMulCUDAILi32EEvPfS0_S0_iiE2Bs[4096];

.visible .entry _Z13MatrixMulCUDAILi16EEvPfS0_S0_ii(
	.param .u64 _Z13MatrixMulCUDAILi16EEvPfS0_S0_ii_param_0,
	.param .u64 _Z13MatrixMulCUDAILi16EEvPfS0_S0_ii_param_1,
	.param .u64 _Z13MatrixMulCUDAILi16EEvPfS0_S0_ii_param_2,
	.param .u32 _Z13MatrixMulCUDAILi16EEvPfS0_S0_ii_param_3,
	.param .u32 _Z13MatrixMulCUDAILi16EEvPfS0_S0_ii_param_4
)
{
	.local .align 8 .b8 	__local_depot0[88];
	.reg .b64 	%SP;
	.reg .b64 	%SPL;
	.reg .pred 	%p<3>;
	.reg .b32 	%r<63>;
	.reg .f32 	%f<8>;
	.reg .b64 	%rd<53>;

	mov.u64 	%SPL, __local_depot0;
	cvta.local.u64 	%SP, %SPL;
	ld.param.u32 	%r2, [_Z13MatrixMulCUDAILi16EEvPfS0_S0_ii_param_4];
	ld.param.u32 	%r1, [_Z13MatrixMulCUDAILi16EEvPfS0_S0_ii_param_3];
	ld.param.u64 	%rd3, [_Z13MatrixMulCUDAILi16EEvPfS0_S0_ii_param_2];
	ld.param.u64 	%rd2, [_Z13MatrixMulCUDAILi16EEvPfS0_S0_ii_param_1];
	ld.param.u64 	%rd1, [_Z13MatrixMulCUDAILi16EEvPfS0_S0_ii_param_0];
	cvta.to.global.u64 	%rd4, %rd3;
	cvta.global.u64 	%rd5, %rd4;
	cvta.to.global.u64 	%rd6, %rd2;
	cvta.global.u64 	%rd7, %rd6;
	cvta.to.global.u64 	%rd8, %rd1;
	cvta.global.u64 	%rd9, %rd8;
	st.u64 	[%SP+0], %rd9;
	st.u64 	[%SP+8], %rd7;
	st.u64 	[%SP+16], %rd5;
	st.u32 	[%SP+24], %r1;
	st.u32 	[%SP+28], %r2;
	mov.u32 	%r3, %ctaid.x;
	st.u32 	[%SP+32], %r3;
	mov.u32 	%r4, %ctaid.y;
	st.u32 	[%SP+36], %r4;
	mov.u32 	%r5, %tid.x;
	st.u32 	[%SP+40], %r5;
	mov.u32 	%r6, %tid.y;
	st.u32 	[%SP+44], %r6;
	ld.u32 	%r7, [%SP+24];
	ld.u32 	%r8, [%SP+36];
	mul.lo.s32 	%r9, %r7, %r8;
	shl.b32 	%r10, %r9, 4;
	st.u32 	[%SP+48], %r10;
	ld.u32 	%r11, [%SP+48];
	ld.u32 	%r12, [%SP+24];
	add.s32 	%r13, %r11, %r12;
	add.s32 	%r14, %r13, -1;
	st.u32 	[%SP+52], %r14;
	mov.u32 	%r15, 16;
	st.u32 	[%SP+56], %r15;
	ld.u32 	%r16, [%SP+32];
	shl.b32 	%r17, %r16, 4;
	st.u32 	[%SP+60], %r17;
	ld.u32 	%r18, [%SP+28];
	shl.b32 	%r19, %r18, 4;
	st.u32 	[%SP+64], %r19;
	mov.u32 	%r20, 0;
	st.u32 	[%SP+68], %r20;
	ld.u32 	%r21, [%SP+48];
	st.u32 	[%SP+72], %r21;
	ld.u32 	%r22, [%SP+60];
	st.u32 	[%SP+76], %r22;
	bra.uni 	LBB0_1;
LBB0_1:
	ld.u32 	%r23, [%SP+72];
	ld.u32 	%r24, [%SP+52];
	setp.gt.s32 	%p1, %r23, %r24;
	@%p1 bra 	LBB0_8;
	bra.uni 	LBB0_2;
LBB0_2:
	ld.u64 	%rd14, [%SP+8];
	ld.u32 	%r39, [%SP+72];
	ld.u32 	%r40, [%SP+24];
	ld.u32 	%r41, [%SP+44];
	mul.lo.s32 	%r42, %r40, %r41;
	add.s32 	%r43, %r39, %r42;
	ld.u32 	%r44, [%SP+40];
	add.s32 	%r45, %r43, %r44;
	cvt.s64.s32 	%rd15, %r45;
	shl.b64 	%rd16, %rd15, 2;
	add.s64 	%rd17, %rd14, %rd16;
	ld.f32 	%f2, [%rd17];
	cvt.s64.s32 	%rd18, %r41;
	mov.u64 	%rd19, _ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2As;
	cvta.shared.u64 	%rd20, %rd19;
	shl.b64 	%rd21, %rd18, 6;
	add.s64 	%rd22, %rd20, %rd21;
	cvt.s64.s32 	%rd23, %r44;
	shl.b64 	%rd24, %rd23, 2;
	add.s64 	%rd25, %rd22, %rd24;
	st.f32 	[%rd25], %f2;
	ld.u64 	%rd26, [%SP+16];
	ld.u32 	%r46, [%SP+76];
	ld.u32 	%r47, [%SP+28];
	ld.u32 	%r48, [%SP+44];
	mul.lo.s32 	%r49, %r47, %r48;
	add.s32 	%r50, %r46, %r49;
	ld.u32 	%r51, [%SP+40];
	add.s32 	%r52, %r50, %r51;
	cvt.s64.s32 	%rd27, %r52;
	shl.b64 	%rd28, %rd27, 2;
	add.s64 	%rd29, %rd26, %rd28;
	ld.f32 	%f3, [%rd29];
	cvt.s64.s32 	%rd30, %r48;
	mov.u64 	%rd31, _ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2Bs;
	cvta.shared.u64 	%rd32, %rd31;
	shl.b64 	%rd33, %rd30, 6;
	add.s64 	%rd34, %rd32, %rd33;
	cvt.s64.s32 	%rd35, %r51;
	shl.b64 	%rd36, %rd35, 2;
	add.s64 	%rd37, %rd34, %rd36;
	st.f32 	[%rd37], %f3;
	bar.sync 	0;
	mov.u32 	%r53, 0;
	st.u32 	[%SP+80], %r53;
	bra.uni 	LBB0_3;
LBB0_3:
	ld.u32 	%r54, [%SP+80];
	setp.gt.s32 	%p2, %r54, 15;
	@%p2 bra 	LBB0_6;
	bra.uni 	LBB0_4;
LBB0_4:
	ld.s32 	%rd38, [%SP+44];
	shl.b64 	%rd39, %rd38, 6;
	mov.u64 	%rd40, _ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2As;
	cvta.shared.u64 	%rd41, %rd40;
	add.s64 	%rd42, %rd41, %rd39;
	ld.s32 	%rd43, [%SP+80];
	shl.b64 	%rd44, %rd43, 2;
	add.s64 	%rd45, %rd42, %rd44;
	ld.f32 	%f4, [%rd45];
	shl.b64 	%rd46, %rd43, 6;
	mov.u64 	%rd47, _ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2Bs;
	cvta.shared.u64 	%rd48, %rd47;
	add.s64 	%rd49, %rd48, %rd46;
	ld.s32 	%rd50, [%SP+40];
	shl.b64 	%rd51, %rd50, 2;
	add.s64 	%rd52, %rd49, %rd51;
	ld.f32 	%f5, [%rd52];
	ld.f32 	%f6, [%SP+68];
	fma.rn.f32 	%f7, %f4, %f5, %f6;
	st.f32 	[%SP+68], %f7;
	bra.uni 	LBB0_5;
LBB0_5:
	ld.u32 	%r61, [%SP+80];
	add.s32 	%r62, %r61, 1;
	st.u32 	[%SP+80], %r62;
	bra.uni 	LBB0_3;
LBB0_6:
	bar.sync 	0;
	bra.uni 	LBB0_7;
LBB0_7:
	ld.u32 	%r55, [%SP+56];
	ld.u32 	%r56, [%SP+72];
	add.s32 	%r57, %r56, %r55;
	st.u32 	[%SP+72], %r57;
	ld.u32 	%r58, [%SP+64];
	ld.u32 	%r59, [%SP+76];
	add.s32 	%r60, %r59, %r58;
	st.u32 	[%SP+76], %r60;
	bra.uni 	LBB0_1;
LBB0_8:
	ld.u32 	%r25, [%SP+28];
	ld.u32 	%r26, [%SP+36];
	mul.lo.s32 	%r27, %r25, %r26;
	shl.b32 	%r28, %r27, 4;
	ld.u32 	%r29, [%SP+32];
	shl.b32 	%r30, %r29, 4;
	add.s32 	%r31, %r28, %r30;
	st.u32 	[%SP+84], %r31;
	ld.f32 	%f1, [%SP+68];
	ld.u64 	%rd10, [%SP+0];
	ld.u32 	%r32, [%SP+84];
	ld.u32 	%r33, [%SP+28];
	ld.u32 	%r34, [%SP+44];
	mul.lo.s32 	%r35, %r33, %r34;
	add.s32 	%r36, %r32, %r35;
	ld.u32 	%r37, [%SP+40];
	add.s32 	%r38, %r36, %r37;
	cvt.s64.s32 	%rd11, %r38;
	shl.b64 	%rd12, %rd11, 2;
	add.s64 	%rd13, %rd10, %rd12;
	st.f32 	[%rd13], %f1;
	ret;

}
	// .globl	_Z13MatrixMulCUDAILi32EEvPfS0_S0_ii
.visible .entry _Z13MatrixMulCUDAILi32EEvPfS0_S0_ii(
	.param .u64 _Z13MatrixMulCUDAILi32EEvPfS0_S0_ii_param_0,
	.param .u64 _Z13MatrixMulCUDAILi32EEvPfS0_S0_ii_param_1,
	.param .u64 _Z13MatrixMulCUDAILi32EEvPfS0_S0_ii_param_2,
	.param .u32 _Z13MatrixMulCUDAILi32EEvPfS0_S0_ii_param_3,
	.param .u32 _Z13MatrixMulCUDAILi32EEvPfS0_S0_ii_param_4
)
{
	.local .align 8 .b8 	__local_depot1[88];
	.reg .b64 	%SP;
	.reg .b64 	%SPL;
	.reg .pred 	%p<3>;
	.reg .b32 	%r<63>;
	.reg .f32 	%f<8>;
	.reg .b64 	%rd<53>;

	mov.u64 	%SPL, __local_depot1;
	cvta.local.u64 	%SP, %SPL;
	ld.param.u32 	%r2, [_Z13MatrixMulCUDAILi32EEvPfS0_S0_ii_param_4];
	ld.param.u32 	%r1, [_Z13MatrixMulCUDAILi32EEvPfS0_S0_ii_param_3];
	ld.param.u64 	%rd3, [_Z13MatrixMulCUDAILi32EEvPfS0_S0_ii_param_2];
	ld.param.u64 	%rd2, [_Z13MatrixMulCUDAILi32EEvPfS0_S0_ii_param_1];
	ld.param.u64 	%rd1, [_Z13MatrixMulCUDAILi32EEvPfS0_S0_ii_param_0];
	cvta.to.global.u64 	%rd4, %rd3;
	cvta.global.u64 	%rd5, %rd4;
	cvta.to.global.u64 	%rd6, %rd2;
	cvta.global.u64 	%rd7, %rd6;
	cvta.to.global.u64 	%rd8, %rd1;
	cvta.global.u64 	%rd9, %rd8;
	st.u64 	[%SP+0], %rd9;
	st.u64 	[%SP+8], %rd7;
	st.u64 	[%SP+16], %rd5;
	st.u32 	[%SP+24], %r1;
	st.u32 	[%SP+28], %r2;
	mov.u32 	%r3, %ctaid.x;
	st.u32 	[%SP+32], %r3;
	mov.u32 	%r4, %ctaid.y;
	st.u32 	[%SP+36], %r4;
	mov.u32 	%r5, %tid.x;
	st.u32 	[%SP+40], %r5;
	mov.u32 	%r6, %tid.y;
	st.u32 	[%SP+44], %r6;
	ld.u32 	%r7, [%SP+24];
	ld.u32 	%r8, [%SP+36];
	mul.lo.s32 	%r9, %r7, %r8;
	shl.b32 	%r10, %r9, 5;
	st.u32 	[%SP+48], %r10;
	ld.u32 	%r11, [%SP+48];
	ld.u32 	%r12, [%SP+24];
	add.s32 	%r13, %r11, %r12;
	add.s32 	%r14, %r13, -1;
	st.u32 	[%SP+52], %r14;
	mov.u32 	%r15, 32;
	st.u32 	[%SP+56], %r15;
	ld.u32 	%r16, [%SP+32];
	shl.b32 	%r17, %r16, 5;
	st.u32 	[%SP+60], %r17;
	ld.u32 	%r18, [%SP+28];
	shl.b32 	%r19, %r18, 5;
	st.u32 	[%SP+64], %r19;
	mov.u32 	%r20, 0;
	st.u32 	[%SP+68], %r20;
	ld.u32 	%r21, [%SP+48];
	st.u32 	[%SP+72], %r21;
	ld.u32 	%r22, [%SP+60];
	st.u32 	[%SP+76], %r22;
	bra.uni 	LBB1_1;
LBB1_1:
	ld.u32 	%r23, [%SP+72];
	ld.u32 	%r24, [%SP+52];
	setp.gt.s32 	%p1, %r23, %r24;
	@%p1 bra 	LBB1_8;
	bra.uni 	LBB1_2;
LBB1_2:
	ld.u64 	%rd14, [%SP+8];
	ld.u32 	%r39, [%SP+72];
	ld.u32 	%r40, [%SP+24];
	ld.u32 	%r41, [%SP+44];
	mul.lo.s32 	%r42, %r40, %r41;
	add.s32 	%r43, %r39, %r42;
	ld.u32 	%r44, [%SP+40];
	add.s32 	%r45, %r43, %r44;
	cvt.s64.s32 	%rd15, %r45;
	shl.b64 	%rd16, %rd15, 2;
	add.s64 	%rd17, %rd14, %rd16;
	ld.f32 	%f2, [%rd17];
	cvt.s64.s32 	%rd18, %r41;
	mov.u64 	%rd19, _ZZ13MatrixMulCUDAILi32EEvPfS0_S0_iiE2As;
	cvta.shared.u64 	%rd20, %rd19;
	shl.b64 	%rd21, %rd18, 7;
	add.s64 	%rd22, %rd20, %rd21;
	cvt.s64.s32 	%rd23, %r44;
	shl.b64 	%rd24, %rd23, 2;
	add.s64 	%rd25, %rd22, %rd24;
	st.f32 	[%rd25], %f2;
	ld.u64 	%rd26, [%SP+16];
	ld.u32 	%r46, [%SP+76];
	ld.u32 	%r47, [%SP+28];
	ld.u32 	%r48, [%SP+44];
	mul.lo.s32 	%r49, %r47, %r48;
	add.s32 	%r50, %r46, %r49;
	ld.u32 	%r51, [%SP+40];
	add.s32 	%r52, %r50, %r51;
	cvt.s64.s32 	%rd27, %r52;
	shl.b64 	%rd28, %rd27, 2;
	add.s64 	%rd29, %rd26, %rd28;
	ld.f32 	%f3, [%rd29];
	cvt.s64.s32 	%rd30, %r48;
	mov.u64 	%rd31, _ZZ13MatrixMulCUDAILi32EEvPfS0_S0_iiE2Bs;
	cvta.shared.u64 	%rd32, %rd31;
	shl.b64 	%rd33, %rd30, 7;
	add.s64 	%rd34, %rd32, %rd33;
	cvt.s64.s32 	%rd35, %r51;
	shl.b64 	%rd36, %rd35, 2;
	add.s64 	%rd37, %rd34, %rd36;
	st.f32 	[%rd37], %f3;
	bar.sync 	0;
	mov.u32 	%r53, 0;
	st.u32 	[%SP+80], %r53;
	bra.uni 	LBB1_3;
LBB1_3:
	ld.u32 	%r54, [%SP+80];
	setp.gt.s32 	%p2, %r54, 31;
	@%p2 bra 	LBB1_6;
	bra.uni 	LBB1_4;
LBB1_4:
	ld.s32 	%rd38, [%SP+44];
	shl.b64 	%rd39, %rd38, 7;
	mov.u64 	%rd40, _ZZ13MatrixMulCUDAILi32EEvPfS0_S0_iiE2As;
	cvta.shared.u64 	%rd41, %rd40;
	add.s64 	%rd42, %rd41, %rd39;
	ld.s32 	%rd43, [%SP+80];
	shl.b64 	%rd44, %rd43, 2;
	add.s64 	%rd45, %rd42, %rd44;
	ld.f32 	%f4, [%rd45];
	shl.b64 	%rd46, %rd43, 7;
	mov.u64 	%rd47, _ZZ13MatrixMulCUDAILi32EEvPfS0_S0_iiE2Bs;
	cvta.shared.u64 	%rd48, %rd47;
	add.s64 	%rd49, %rd48, %rd46;
	ld.s32 	%rd50, [%SP+40];
	shl.b64 	%rd51, %rd50, 2;
	add.s64 	%rd52, %rd49, %rd51;
	ld.f32 	%f5, [%rd52];
	ld.f32 	%f6, [%SP+68];
	fma.rn.f32 	%f7, %f4, %f5, %f6;
	st.f32 	[%SP+68], %f7;
	bra.uni 	LBB1_5;
LBB1_5:
	ld.u32 	%r61, [%SP+80];
	add.s32 	%r62, %r61, 1;
	st.u32 	[%SP+80], %r62;
	bra.uni 	LBB1_3;
LBB1_6:
	bar.sync 	0;
	bra.uni 	LBB1_7;
LBB1_7:
	ld.u32 	%r55, [%SP+56];
	ld.u32 	%r56, [%SP+72];
	add.s32 	%r57, %r56, %r55;
	st.u32 	[%SP+72], %r57;
	ld.u32 	%r58, [%SP+64];
	ld.u32 	%r59, [%SP+76];
	add.s32 	%r60, %r59, %r58;
	st.u32 	[%SP+76], %r60;
	bra.uni 	LBB1_1;
LBB1_8:
	ld.u32 	%r25, [%SP+28];
	ld.u32 	%r26, [%SP+36];
	mul.lo.s32 	%r27, %r25, %r26;
	shl.b32 	%r28, %r27, 5;
	ld.u32 	%r29, [%SP+32];
	shl.b32 	%r30, %r29, 5;
	add.s32 	%r31, %r28, %r30;
	st.u32 	[%SP+84], %r31;
	ld.f32 	%f1, [%SP+68];
	ld.u64 	%rd10, [%SP+0];
	ld.u32 	%r32, [%SP+84];
	ld.u32 	%r33, [%SP+28];
	ld.u32 	%r34, [%SP+44];
	mul.lo.s32 	%r35, %r33, %r34;
	add.s32 	%r36, %r32, %r35;
	ld.u32 	%r37, [%SP+40];
	add.s32 	%r38, %r36, %r37;
	cvt.s64.s32 	%rd11, %r38;
	shl.b64 	%rd12, %rd11, 2;
	add.s64 	%rd13, %rd10, %rd12;
	st.f32 	[%rd13], %f1;
	ret;

}
