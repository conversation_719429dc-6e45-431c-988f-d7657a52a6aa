/* Generated by CustomLLVMToCppConverter */
#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include <math.h>
#include <stdio.h>
#include <stdlib.h>

/* CUDA Runtime Headers */
#ifdef __CUDACC__
#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#endif

/* Helper Macros */
#ifndef __forceinline
#define __forceinline inline
#endif

#ifndef __device__
#define __device__
#endif

#ifndef __global__
#define __global__
#endif

/* CUDA API Function Declarations */
#ifdef __cplusplus
extern "C" {
#endif
uint32_t cudaGetDeviceCount(uint32_t* count);
uint32_t cudaSetDevice(uint32_t device);
uint32_t cudaGetDeviceProperties(void* prop, uint32_t device);
const char* cudaGetErrorString(uint32_t error);
uint32_t cudaHostAlloc(void** ptr, uint64_t size, uint32_t flags);
uint32_t cudaFreeHost(void* ptr);
uint32_t cudaMemcpyAsync(void* dst, void* src, uint64_t count, uint32_t kind, void* stream);
uint32_t cudaEventCreate(void** event);
uint32_t cudaEventDestroy(void* event);
uint32_t cudaEventRecord(void* event, void* stream);
uint32_t cudaEventSynchronize(void* event);
uint32_t cudaEventElapsedTime(float* ms, void* start, void* end);
uint32_t cudaStreamCreateWithFlags(void** stream, uint32_t flags);
uint32_t cudaStreamSynchronize(void* stream);
uint32_t cudaProfilerStart();
uint32_t cudaProfilerStop();
extern FILE* stderr;
extern FILE* stdout;
extern FILE* stdin;
#ifdef __cplusplus
}
#endif



/* Type Declarations */
struct array_16_float_ {
  float array[16];
};

struct array_32_float_ {
  float array[32];
};

struct struct___cuda_builtin_blockIdx_t {
  uint8_t field0;
};

struct struct___cuda_builtin_threadIdx_t {
  uint8_t field0;
};

struct array_16_struct_array_16_float_ {
  struct array_16_float_ array[16];
};

struct array_32_struct_array_32_float_ {
  struct array_32_float_ array[32];
};

typedef uint32_t (*func_ptr_0)();
typedef void (*func_ptr_1)();


/* Global Variable Declarations */


/* Function Declarations */
void _Z13MatrixMulCUDAILi16EEvPfS0_S0_ii(void* tmp2, void* tmp3, void* tmp4, uint32_t tmp5, uint32_t tmp6);
uint32_t _ZN25__cuda_builtin_blockIdx_t17__fetch_builtin_xEv();
uint32_t _ZN25__cuda_builtin_blockIdx_t17__fetch_builtin_yEv();
uint32_t _ZN26__cuda_builtin_threadIdx_t17__fetch_builtin_xEv();
uint32_t _ZN26__cuda_builtin_threadIdx_t17__fetch_builtin_yEv();
void _Z13MatrixMulCUDAILi32EEvPfS0_S0_ii(void* tmp7, void* tmp8, void* tmp9, uint32_t tmp10, uint32_t tmp11);


/* Helper Functions */
/* CUDA Built-in Functions */
#ifdef __CUDACC__
// CUDA built-ins are provided by the runtime
#else
// Emulated CUDA built-ins for host compilation using simulation system
#include "CudaSimulation.h"

// CUDA built-in structures using simulation system
struct dim3 { unsigned int x, y, z; };

// Thread-local CUDA built-in variables
static inline struct dim3 get_cuda_threadIdx() { dim3_t t = get_threadIdx(); struct dim3 result = {t.x, t.y, t.z}; return result; }
static inline struct dim3 get_cuda_blockIdx() { dim3_t t = get_blockIdx(); struct dim3 result = {t.x, t.y, t.z}; return result; }
static inline struct dim3 get_cuda_blockDim() { dim3_t t = get_blockDim(); struct dim3 result = {t.x, t.y, t.z}; return result; }
static inline struct dim3 get_cuda_gridDim() { dim3_t t = get_gridDim(); struct dim3 result = {t.x, t.y, t.z}; return result; }
#define threadIdx get_cuda_threadIdx()
#define blockIdx get_cuda_blockIdx()
#define blockDim get_cuda_blockDim()
#define gridDim get_cuda_gridDim()

#endif

/* LLVM Intrinsic Helpers */
__forceinline int llvm_ctpop_i32(int x) {
  int count = 0;
  while (x) { count += x & 1; x >>= 1; }
  return count;
}

__forceinline int llvm_ctlz_i32(int x) {
  if (x == 0) return 32;
  int count = 0;
  while ((x & 0x80000000) == 0) { count++; x <<= 1; }
  return count;
}

__forceinline int llvm_cttz_i32(int x) {
  if (x == 0) return 32;
  int count = 0;
  while ((x & 1) == 0) { count++; x >>= 1; }
  return count;
}



/* Global Variable Definitions */

void _Z13MatrixMulCUDAILi16EEvPfS0_S0_ii(void* tmp2, void* tmp3, void* tmp4, uint32_t tmp5, uint32_t tmp6) {
  /* Local Variables */
  uint32_t tmp12;
  uint32_t tmp13;
  uint32_t tmp14;
  uint32_t tmp15;
  uint32_t tmp16;
  uint32_t tmp17;
  uint32_t tmp18;
  uint32_t tmp19;
  uint32_t tmp20;
  uint32_t tmp21;
  uint32_t tmp22;
  uint32_t tmp23;
  uint32_t tmp24;
  uint32_t tmp25;
  uint32_t tmp26;
  uint32_t tmp27;
  uint32_t tmp28;
  uint32_t tmp29;
  uint32_t tmp30;
  uint32_t tmp31;
  bool tmp32;
  void* tmp33;
  uint32_t tmp34;
  uint32_t tmp35;
  uint32_t tmp36;
  uint32_t tmp37;
  uint32_t tmp38;
  uint32_t tmp39;
  uint32_t tmp40;
  uint64_t tmp41;
  void* tmp42;
  float tmp43;
  uint32_t tmp44;
  uint64_t tmp45;
  void* tmp46;
  uint32_t tmp47;
  uint64_t tmp48;
  void* tmp49;
  void* tmp50;
  uint32_t tmp51;
  uint32_t tmp52;
  uint32_t tmp53;
  uint32_t tmp54;
  uint32_t tmp55;
  uint32_t tmp56;
  uint32_t tmp57;
  uint64_t tmp58;
  void* tmp59;
  float tmp60;
  uint32_t tmp61;
  uint64_t tmp62;
  void* tmp63;
  uint32_t tmp64;
  uint64_t tmp65;
  void* tmp66;
  uint32_t tmp67;
  bool tmp68;
  uint32_t tmp69;
  uint64_t tmp70;
  void* tmp71;
  uint32_t tmp72;
  uint64_t tmp73;
  void* tmp74;
  float tmp75;
  uint32_t tmp76;
  uint64_t tmp77;
  void* tmp78;
  uint32_t tmp79;
  uint64_t tmp80;
  void* tmp81;
  float tmp82;
  float tmp83;
  float tmp84;
  float tmp85;
  uint32_t tmp86;
  uint32_t tmp87;
  uint32_t tmp88;
  uint32_t tmp89;
  uint32_t tmp90;
  uint32_t tmp91;
  uint32_t tmp92;
  uint32_t tmp93;
  uint32_t tmp94;
  uint32_t tmp95;
  uint32_t tmp96;
  uint32_t tmp97;
  uint32_t tmp98;
  uint32_t tmp99;
  uint32_t tmp100;
  float tmp101;
  void* tmp102;
  uint32_t tmp103;
  uint32_t tmp104;
  uint32_t tmp105;
  uint32_t tmp106;
  uint32_t tmp107;
  uint32_t tmp108;
  uint32_t tmp109;
  uint64_t tmp110;
  void* tmp111;

  void* tmp112_storage = NULL;
  void** tmp112 = &tmp112_storage; /* alloca */
  void* tmp113_storage = NULL;
  void** tmp113 = &tmp113_storage; /* alloca */
  void* tmp114_storage = NULL;
  void** tmp114 = &tmp114_storage; /* alloca */
  uint32_t tmp115_storage = 0;
  uint32_t* tmp115 = &tmp115_storage; /* alloca */
  uint32_t tmp116_storage = 0;
  uint32_t* tmp116 = &tmp116_storage; /* alloca */
  uint32_t tmp117_storage = 0;
  uint32_t* tmp117 = &tmp117_storage; /* alloca */
  uint32_t tmp118_storage = 0;
  uint32_t* tmp118 = &tmp118_storage; /* alloca */
  uint32_t tmp119_storage = 0;
  uint32_t* tmp119 = &tmp119_storage; /* alloca */
  uint32_t tmp120_storage = 0;
  uint32_t* tmp120 = &tmp120_storage; /* alloca */
  uint32_t tmp121_storage = 0;
  uint32_t* tmp121 = &tmp121_storage; /* alloca */
  uint32_t tmp122_storage = 0;
  uint32_t* tmp122 = &tmp122_storage; /* alloca */
  uint32_t tmp123_storage = 0;
  uint32_t* tmp123 = &tmp123_storage; /* alloca */
  uint32_t tmp124_storage = 0;
  uint32_t* tmp124 = &tmp124_storage; /* alloca */
  uint32_t tmp125_storage = 0;
  uint32_t* tmp125 = &tmp125_storage; /* alloca */
  float tmp126_storage = 0;
  float* tmp126 = &tmp126_storage; /* alloca */
  uint32_t tmp127_storage = 0;
  uint32_t* tmp127 = &tmp127_storage; /* alloca */
  uint32_t tmp128_storage = 0;
  uint32_t* tmp128 = &tmp128_storage; /* alloca */
  uint32_t tmp129_storage = 0;
  uint32_t* tmp129 = &tmp129_storage; /* alloca */
  uint32_t tmp130_storage = 0;
  uint32_t* tmp130 = &tmp130_storage; /* alloca */
  *(void**)tmp112 = tmp2;
  *(void**)tmp113 = tmp3;
  *(void**)tmp114 = tmp4;
  *(uint32_t*)tmp115 = tmp5;
  *(uint32_t*)tmp116 = tmp6;
  tmp12 = _ZN25__cuda_builtin_blockIdx_t17__fetch_builtin_xEv();
  *(uint32_t*)tmp117 = tmp12;
  tmp13 = _ZN25__cuda_builtin_blockIdx_t17__fetch_builtin_yEv();
  *(uint32_t*)tmp118 = tmp13;
  tmp14 = _ZN26__cuda_builtin_threadIdx_t17__fetch_builtin_xEv();
  *(uint32_t*)tmp119 = tmp14;
  tmp15 = _ZN26__cuda_builtin_threadIdx_t17__fetch_builtin_yEv();
  *(uint32_t*)tmp120 = tmp15;
  tmp16 = *(uint32_t*)tmp115;
  tmp17 = tmp16 * 16;
  tmp18 = *(uint32_t*)tmp118;
  tmp19 = tmp17 * tmp18;
  *(uint32_t*)tmp121 = tmp19;
  tmp20 = *(uint32_t*)tmp121;
  tmp21 = *(uint32_t*)tmp115;
  tmp22 = tmp20 + tmp21;
  tmp23 = tmp22 - 1;
  *(uint32_t*)tmp122 = tmp23;
  *(uint32_t*)tmp123 = 16;
  tmp24 = *(uint32_t*)tmp117;
  tmp25 = 16 * tmp24;
  *(uint32_t*)tmp124 = tmp25;
  tmp26 = *(uint32_t*)tmp116;
  tmp27 = 16 * tmp26;
  *(uint32_t*)tmp125 = tmp27;
  *(float*)tmp126 = 0.000000f;
  tmp28 = *(uint32_t*)tmp121;
  *(uint32_t*)tmp127 = tmp28;
  tmp29 = *(uint32_t*)tmp124;
  *(uint32_t*)tmp128 = tmp29;
  goto tmp131;
tmp131:
  tmp30 = *(uint32_t*)tmp127;
  tmp31 = *(uint32_t*)tmp122;
  tmp32 = tmp30 <= tmp31;
  if (tmp32) {
    goto tmp132;
  } else {
    goto tmp133;
  }
tmp132:
  tmp33 = *(void**)tmp113;
  tmp34 = *(uint32_t*)tmp127;
  tmp35 = *(uint32_t*)tmp115;
  tmp36 = *(uint32_t*)tmp120;
  tmp37 = tmp35 * tmp36;
  tmp38 = tmp34 + tmp37;
  tmp39 = *(uint32_t*)tmp119;
  tmp40 = tmp38 + tmp39;
  tmp41 = (uint64_t)tmp40;
  tmp42 = (void*)(&((float*)tmp33)[tmp41]);
  tmp43 = *(float*)tmp42;
  tmp44 = *(uint32_t*)tmp120;
  tmp45 = (uint64_t)tmp44;
  tmp46 = (void*)((uint32_t*)get_block_shared_memory("_ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2As", 64) + tmp45);
  tmp47 = *(uint32_t*)tmp119;
  tmp48 = (uint64_t)tmp47;
  tmp49 = (void*)((char*)tmp46) + (tmp48 * 4);
  *(float*)tmp49 = tmp43;
  tmp50 = *(void**)tmp114;
  tmp51 = *(uint32_t*)tmp128;
  tmp52 = *(uint32_t*)tmp116;
  tmp53 = *(uint32_t*)tmp120;
  tmp54 = tmp52 * tmp53;
  tmp55 = tmp51 + tmp54;
  tmp56 = *(uint32_t*)tmp119;
  tmp57 = tmp55 + tmp56;
  tmp58 = (uint64_t)tmp57;
  tmp59 = (void*)(&((float*)tmp50)[tmp58]);
  tmp60 = *(float*)tmp59;
  tmp61 = *(uint32_t*)tmp120;
  tmp62 = (uint64_t)tmp61;
  tmp63 = (void*)((uint32_t*)get_block_shared_memory("_ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2Bs", 64) + tmp62);
  tmp64 = *(uint32_t*)tmp119;
  tmp65 = (uint64_t)tmp64;
  tmp66 = (void*)((char*)tmp63) + (tmp65 * 4);
  *(float*)tmp66 = tmp60;
  __syncthreads();
  *(uint32_t*)tmp129 = 0;
  goto tmp134;
tmp134:
  tmp67 = *(uint32_t*)tmp129;
  tmp68 = tmp67 < 16;
  if (tmp68) {
    goto tmp135;
  } else {
    goto tmp136;
  }
tmp135:
  tmp69 = *(uint32_t*)tmp120;
  tmp70 = (uint64_t)tmp69;
  tmp71 = (void*)((uint32_t*)get_block_shared_memory("_ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2As", 64) + tmp70);
  tmp72 = *(uint32_t*)tmp129;
  tmp73 = (uint64_t)tmp72;
  tmp74 = (void*)((char*)tmp71) + (tmp73 * 4);
  tmp75 = *(float*)tmp74;
  tmp76 = *(uint32_t*)tmp129;
  tmp77 = (uint64_t)tmp76;
  tmp78 = (void*)((uint32_t*)get_block_shared_memory("_ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2Bs", 64) + tmp77);
  tmp79 = *(uint32_t*)tmp119;
  tmp80 = (uint64_t)tmp79;
  tmp81 = (void*)((char*)tmp78) + (tmp80 * 4);
  tmp82 = *(float*)tmp81;
  tmp83 = tmp75 * tmp82;
  tmp84 = *(float*)tmp126;
  tmp85 = tmp84 + tmp83;
  *(float*)tmp126 = tmp85;
  goto tmp137;
tmp137:
  tmp86 = *(uint32_t*)tmp129;
  tmp87 = tmp86 + 1;
  *(uint32_t*)tmp129 = tmp87;
  goto tmp134;
tmp136:
  __syncthreads();
  goto tmp138;
tmp138:
  tmp88 = *(uint32_t*)tmp123;
  tmp89 = *(uint32_t*)tmp127;
  tmp90 = tmp89 + tmp88;
  *(uint32_t*)tmp127 = tmp90;
  tmp91 = *(uint32_t*)tmp125;
  tmp92 = *(uint32_t*)tmp128;
  tmp93 = tmp92 + tmp91;
  *(uint32_t*)tmp128 = tmp93;
  goto tmp131;
tmp133:
  tmp94 = *(uint32_t*)tmp116;
  tmp95 = tmp94 * 16;
  tmp96 = *(uint32_t*)tmp118;
  tmp97 = tmp95 * tmp96;
  tmp98 = *(uint32_t*)tmp117;
  tmp99 = 16 * tmp98;
  tmp100 = tmp97 + tmp99;
  *(uint32_t*)tmp130 = tmp100;
  tmp101 = *(float*)tmp126;
  tmp102 = *(void**)tmp112;
  tmp103 = *(uint32_t*)tmp130;
  tmp104 = *(uint32_t*)tmp116;
  tmp105 = *(uint32_t*)tmp120;
  tmp106 = tmp104 * tmp105;
  tmp107 = tmp103 + tmp106;
  tmp108 = *(uint32_t*)tmp119;
  tmp109 = tmp107 + tmp108;
  tmp110 = (uint64_t)tmp109;
  tmp111 = (void*)(&((float*)tmp102)[tmp110]);
  *(float*)tmp111 = tmp101;
  return;
}

uint32_t _ZN25__cuda_builtin_blockIdx_t17__fetch_builtin_xEv() {
  /* Local Variables */
  uint32_t tmp139;

  tmp139 = blockIdx.x;
  return tmp139;
}

uint32_t _ZN25__cuda_builtin_blockIdx_t17__fetch_builtin_yEv() {
  /* Local Variables */
  uint32_t tmp140;

  tmp140 = blockIdx.y;
  return tmp140;
}

uint32_t _ZN26__cuda_builtin_threadIdx_t17__fetch_builtin_xEv() {
  /* Local Variables */
  uint32_t tmp141;

  tmp141 = threadIdx.x;
  return tmp141;
}

uint32_t _ZN26__cuda_builtin_threadIdx_t17__fetch_builtin_yEv() {
  /* Local Variables */
  uint32_t tmp142;

  tmp142 = threadIdx.y;
  return tmp142;
}

void _Z13MatrixMulCUDAILi32EEvPfS0_S0_ii(void* tmp7, void* tmp8, void* tmp9, uint32_t tmp10, uint32_t tmp11) {
  /* Local Variables */
  uint32_t tmp143;
  uint32_t tmp144;
  uint32_t tmp145;
  uint32_t tmp146;
  uint32_t tmp147;
  uint32_t tmp148;
  uint32_t tmp149;
  uint32_t tmp150;
  uint32_t tmp151;
  uint32_t tmp152;
  uint32_t tmp153;
  uint32_t tmp154;
  uint32_t tmp155;
  uint32_t tmp156;
  uint32_t tmp157;
  uint32_t tmp158;
  uint32_t tmp159;
  uint32_t tmp160;
  uint32_t tmp161;
  uint32_t tmp162;
  bool tmp163;
  void* tmp164;
  uint32_t tmp165;
  uint32_t tmp166;
  uint32_t tmp167;
  uint32_t tmp168;
  uint32_t tmp169;
  uint32_t tmp170;
  uint32_t tmp171;
  uint64_t tmp172;
  void* tmp173;
  float tmp174;
  uint32_t tmp175;
  uint64_t tmp176;
  void* tmp177;
  uint32_t tmp178;
  uint64_t tmp179;
  void* tmp180;
  void* tmp181;
  uint32_t tmp182;
  uint32_t tmp183;
  uint32_t tmp184;
  uint32_t tmp185;
  uint32_t tmp186;
  uint32_t tmp187;
  uint32_t tmp188;
  uint64_t tmp189;
  void* tmp190;
  float tmp191;
  uint32_t tmp192;
  uint64_t tmp193;
  void* tmp194;
  uint32_t tmp195;
  uint64_t tmp196;
  void* tmp197;
  uint32_t tmp198;
  bool tmp199;
  uint32_t tmp200;
  uint64_t tmp201;
  void* tmp202;
  uint32_t tmp203;
  uint64_t tmp204;
  void* tmp205;
  float tmp206;
  uint32_t tmp207;
  uint64_t tmp208;
  void* tmp209;
  uint32_t tmp210;
  uint64_t tmp211;
  void* tmp212;
  float tmp213;
  float tmp214;
  float tmp215;
  float tmp216;
  uint32_t tmp217;
  uint32_t tmp218;
  uint32_t tmp219;
  uint32_t tmp220;
  uint32_t tmp221;
  uint32_t tmp222;
  uint32_t tmp223;
  uint32_t tmp224;
  uint32_t tmp225;
  uint32_t tmp226;
  uint32_t tmp227;
  uint32_t tmp228;
  uint32_t tmp229;
  uint32_t tmp230;
  uint32_t tmp231;
  float tmp232;
  void* tmp233;
  uint32_t tmp234;
  uint32_t tmp235;
  uint32_t tmp236;
  uint32_t tmp237;
  uint32_t tmp238;
  uint32_t tmp239;
  uint32_t tmp240;
  uint64_t tmp241;
  void* tmp242;

  void* tmp243_storage = NULL;
  void** tmp243 = &tmp243_storage; /* alloca */
  void* tmp244_storage = NULL;
  void** tmp244 = &tmp244_storage; /* alloca */
  void* tmp245_storage = NULL;
  void** tmp245 = &tmp245_storage; /* alloca */
  uint32_t tmp246_storage = 0;
  uint32_t* tmp246 = &tmp246_storage; /* alloca */
  uint32_t tmp247_storage = 0;
  uint32_t* tmp247 = &tmp247_storage; /* alloca */
  uint32_t tmp248_storage = 0;
  uint32_t* tmp248 = &tmp248_storage; /* alloca */
  uint32_t tmp249_storage = 0;
  uint32_t* tmp249 = &tmp249_storage; /* alloca */
  uint32_t tmp250_storage = 0;
  uint32_t* tmp250 = &tmp250_storage; /* alloca */
  uint32_t tmp251_storage = 0;
  uint32_t* tmp251 = &tmp251_storage; /* alloca */
  uint32_t tmp252_storage = 0;
  uint32_t* tmp252 = &tmp252_storage; /* alloca */
  uint32_t tmp253_storage = 0;
  uint32_t* tmp253 = &tmp253_storage; /* alloca */
  uint32_t tmp254_storage = 0;
  uint32_t* tmp254 = &tmp254_storage; /* alloca */
  uint32_t tmp255_storage = 0;
  uint32_t* tmp255 = &tmp255_storage; /* alloca */
  uint32_t tmp256_storage = 0;
  uint32_t* tmp256 = &tmp256_storage; /* alloca */
  float tmp257_storage = 0;
  float* tmp257 = &tmp257_storage; /* alloca */
  uint32_t tmp258_storage = 0;
  uint32_t* tmp258 = &tmp258_storage; /* alloca */
  uint32_t tmp259_storage = 0;
  uint32_t* tmp259 = &tmp259_storage; /* alloca */
  uint32_t tmp260_storage = 0;
  uint32_t* tmp260 = &tmp260_storage; /* alloca */
  uint32_t tmp261_storage = 0;
  uint32_t* tmp261 = &tmp261_storage; /* alloca */
  *(void**)tmp243 = tmp7;
  *(void**)tmp244 = tmp8;
  *(void**)tmp245 = tmp9;
  *(uint32_t*)tmp246 = tmp10;
  *(uint32_t*)tmp247 = tmp11;
  tmp143 = _ZN25__cuda_builtin_blockIdx_t17__fetch_builtin_xEv();
  *(uint32_t*)tmp248 = tmp143;
  tmp144 = _ZN25__cuda_builtin_blockIdx_t17__fetch_builtin_yEv();
  *(uint32_t*)tmp249 = tmp144;
  tmp145 = _ZN26__cuda_builtin_threadIdx_t17__fetch_builtin_xEv();
  *(uint32_t*)tmp250 = tmp145;
  tmp146 = _ZN26__cuda_builtin_threadIdx_t17__fetch_builtin_yEv();
  *(uint32_t*)tmp251 = tmp146;
  tmp147 = *(uint32_t*)tmp246;
  tmp148 = tmp147 * 32;
  tmp149 = *(uint32_t*)tmp249;
  tmp150 = tmp148 * tmp149;
  *(uint32_t*)tmp252 = tmp150;
  tmp151 = *(uint32_t*)tmp252;
  tmp152 = *(uint32_t*)tmp246;
  tmp153 = tmp151 + tmp152;
  tmp154 = tmp153 - 1;
  *(uint32_t*)tmp253 = tmp154;
  *(uint32_t*)tmp254 = 32;
  tmp155 = *(uint32_t*)tmp248;
  tmp156 = 32 * tmp155;
  *(uint32_t*)tmp255 = tmp156;
  tmp157 = *(uint32_t*)tmp247;
  tmp158 = 32 * tmp157;
  *(uint32_t*)tmp256 = tmp158;
  *(float*)tmp257 = 0.000000f;
  tmp159 = *(uint32_t*)tmp252;
  *(uint32_t*)tmp258 = tmp159;
  tmp160 = *(uint32_t*)tmp255;
  *(uint32_t*)tmp259 = tmp160;
  goto tmp262;
tmp262:
  tmp161 = *(uint32_t*)tmp258;
  tmp162 = *(uint32_t*)tmp253;
  tmp163 = tmp161 <= tmp162;
  if (tmp163) {
    goto tmp263;
  } else {
    goto tmp264;
  }
tmp263:
  tmp164 = *(void**)tmp244;
  tmp165 = *(uint32_t*)tmp258;
  tmp166 = *(uint32_t*)tmp246;
  tmp167 = *(uint32_t*)tmp251;
  tmp168 = tmp166 * tmp167;
  tmp169 = tmp165 + tmp168;
  tmp170 = *(uint32_t*)tmp250;
  tmp171 = tmp169 + tmp170;
  tmp172 = (uint64_t)tmp171;
  tmp173 = (void*)(&((float*)tmp164)[tmp172]);
  tmp174 = *(float*)tmp173;
  tmp175 = *(uint32_t*)tmp251;
  tmp176 = (uint64_t)tmp175;
  tmp177 = (void*)((uint32_t*)get_block_shared_memory("_ZZ13MatrixMulCUDAILi32EEvPfS0_S0_iiE2As", 128) + tmp176);
  tmp178 = *(uint32_t*)tmp250;
  tmp179 = (uint64_t)tmp178;
  tmp180 = (void*)((char*)tmp177) + (tmp179 * 4);
  *(float*)tmp180 = tmp174;
  tmp181 = *(void**)tmp245;
  tmp182 = *(uint32_t*)tmp259;
  tmp183 = *(uint32_t*)tmp247;
  tmp184 = *(uint32_t*)tmp251;
  tmp185 = tmp183 * tmp184;
  tmp186 = tmp182 + tmp185;
  tmp187 = *(uint32_t*)tmp250;
  tmp188 = tmp186 + tmp187;
  tmp189 = (uint64_t)tmp188;
  tmp190 = (void*)(&((float*)tmp181)[tmp189]);
  tmp191 = *(float*)tmp190;
  tmp192 = *(uint32_t*)tmp251;
  tmp193 = (uint64_t)tmp192;
  tmp194 = (void*)((uint32_t*)get_block_shared_memory("_ZZ13MatrixMulCUDAILi32EEvPfS0_S0_iiE2Bs", 128) + tmp193);
  tmp195 = *(uint32_t*)tmp250;
  tmp196 = (uint64_t)tmp195;
  tmp197 = (void*)((char*)tmp194) + (tmp196 * 4);
  *(float*)tmp197 = tmp191;
  __syncthreads();
  *(uint32_t*)tmp260 = 0;
  goto tmp265;
tmp265:
  tmp198 = *(uint32_t*)tmp260;
  tmp199 = tmp198 < 32;
  if (tmp199) {
    goto tmp266;
  } else {
    goto tmp267;
  }
tmp266:
  tmp200 = *(uint32_t*)tmp251;
  tmp201 = (uint64_t)tmp200;
  tmp202 = (void*)((uint32_t*)get_block_shared_memory("_ZZ13MatrixMulCUDAILi32EEvPfS0_S0_iiE2As", 128) + tmp201);
  tmp203 = *(uint32_t*)tmp260;
  tmp204 = (uint64_t)tmp203;
  tmp205 = (void*)((char*)tmp202) + (tmp204 * 4);
  tmp206 = *(float*)tmp205;
  tmp207 = *(uint32_t*)tmp260;
  tmp208 = (uint64_t)tmp207;
  tmp209 = (void*)((uint32_t*)get_block_shared_memory("_ZZ13MatrixMulCUDAILi32EEvPfS0_S0_iiE2Bs", 128) + tmp208);
  tmp210 = *(uint32_t*)tmp250;
  tmp211 = (uint64_t)tmp210;
  tmp212 = (void*)((char*)tmp209) + (tmp211 * 4);
  tmp213 = *(float*)tmp212;
  tmp214 = tmp206 * tmp213;
  tmp215 = *(float*)tmp257;
  tmp216 = tmp215 + tmp214;
  *(float*)tmp257 = tmp216;
  goto tmp268;
tmp268:
  tmp217 = *(uint32_t*)tmp260;
  tmp218 = tmp217 + 1;
  *(uint32_t*)tmp260 = tmp218;
  goto tmp265;
tmp267:
  __syncthreads();
  goto tmp269;
tmp269:
  tmp219 = *(uint32_t*)tmp254;
  tmp220 = *(uint32_t*)tmp258;
  tmp221 = tmp220 + tmp219;
  *(uint32_t*)tmp258 = tmp221;
  tmp222 = *(uint32_t*)tmp256;
  tmp223 = *(uint32_t*)tmp259;
  tmp224 = tmp223 + tmp222;
  *(uint32_t*)tmp259 = tmp224;
  goto tmp262;
tmp264:
  tmp225 = *(uint32_t*)tmp247;
  tmp226 = tmp225 * 32;
  tmp227 = *(uint32_t*)tmp249;
  tmp228 = tmp226 * tmp227;
  tmp229 = *(uint32_t*)tmp248;
  tmp230 = 32 * tmp229;
  tmp231 = tmp228 + tmp230;
  *(uint32_t*)tmp261 = tmp231;
  tmp232 = *(float*)tmp257;
  tmp233 = *(void**)tmp243;
  tmp234 = *(uint32_t*)tmp261;
  tmp235 = *(uint32_t*)tmp247;
  tmp236 = *(uint32_t*)tmp251;
  tmp237 = tmp235 * tmp236;
  tmp238 = tmp234 + tmp237;
  tmp239 = *(uint32_t*)tmp250;
  tmp240 = tmp238 + tmp239;
  tmp241 = (uint64_t)tmp240;
  tmp242 = (void*)(&((float*)tmp233)[tmp241]);
  *(float*)tmp242 = tmp232;
  return;
}

