	.text
	.file	"matrixMul.cu"
	.globl	_Z16checkCmdLineFlagiPPKcS0_    # -- Begin function _Z16checkCmdLineFlagiPPKcS0_
	.p2align	4, 0x90
	.type	_Z16checkCmdLineFlagiPPKcS0_,@function
_Z16checkCmdLineFlagiPPKcS0_:           # @_Z16checkCmdLineFlagiPPKcS0_
	.cfi_startproc
# %bb.0:
	pushq	%rbp
	.cfi_def_cfa_offset 16
	.cfi_offset %rbp, -16
	movq	%rsp, %rbp
	.cfi_def_cfa_register %rbp
	subq	$32, %rsp
	movl	%edi, -8(%rbp)
	movq	%rsi, -16(%rbp)
	movq	%rdx, -24(%rbp)
	movl	$1, -28(%rbp)
.LBB0_1:                                # =>This Inner Loop Header: Depth=1
	movl	-28(%rbp), %eax
	cmpl	-8(%rbp), %eax
	jge	.LBB0_6
# %bb.2:                                #   in Loop: Header=BB0_1 Depth=1
	movq	-16(%rbp), %rax
	movslq	-28(%rbp), %rcx
	movq	(%rax,%rcx,8), %rdi
	movq	-24(%rbp), %rsi
	callq	strstr
	cmpq	$0, %rax
	je	.LBB0_4
# %bb.3:
	movb	$1, -1(%rbp)
	jmp	.LBB0_7
.LBB0_4:                                #   in Loop: Header=BB0_1 Depth=1
	jmp	.LBB0_5
.LBB0_5:                                #   in Loop: Header=BB0_1 Depth=1
	movl	-28(%rbp), %eax
	addl	$1, %eax
	movl	%eax, -28(%rbp)
	jmp	.LBB0_1
.LBB0_6:
	movb	$0, -1(%rbp)
.LBB0_7:
	movb	-1(%rbp), %al
	andb	$1, %al
	movzbl	%al, %eax
	addq	$32, %rsp
	popq	%rbp
	.cfi_def_cfa %rsp, 8
	retq
.Lfunc_end0:
	.size	_Z16checkCmdLineFlagiPPKcS0_, .Lfunc_end0-_Z16checkCmdLineFlagiPPKcS0_
	.cfi_endproc
                                        # -- End function
	.globl	_Z21getCmdLineArgumentIntiPPKcS0_ # -- Begin function _Z21getCmdLineArgumentIntiPPKcS0_
	.p2align	4, 0x90
	.type	_Z21getCmdLineArgumentIntiPPKcS0_,@function
_Z21getCmdLineArgumentIntiPPKcS0_:      # @_Z21getCmdLineArgumentIntiPPKcS0_
	.cfi_startproc
# %bb.0:
	pushq	%rbp
	.cfi_def_cfa_offset 16
	.cfi_offset %rbp, -16
	movq	%rsp, %rbp
	.cfi_def_cfa_register %rbp
	subq	$32, %rsp
	movl	%edi, -8(%rbp)
	movq	%rsi, -16(%rbp)
	movq	%rdx, -24(%rbp)
	movl	$1, -28(%rbp)
.LBB1_1:                                # =>This Inner Loop Header: Depth=1
	movl	-28(%rbp), %eax
	cmpl	-8(%rbp), %eax
	jge	.LBB1_8
# %bb.2:                                #   in Loop: Header=BB1_1 Depth=1
	movq	-16(%rbp), %rax
	movslq	-28(%rbp), %rcx
	movq	(%rax,%rcx,8), %rdi
	movq	-24(%rbp), %rsi
	callq	strstr
	cmpq	$0, %rax
	je	.LBB1_6
# %bb.3:                                #   in Loop: Header=BB1_1 Depth=1
	movl	-28(%rbp), %eax
	addl	$1, %eax
	cmpl	-8(%rbp), %eax
	jge	.LBB1_5
# %bb.4:
	movq	-16(%rbp), %rax
	movl	-28(%rbp), %ecx
	addl	$1, %ecx
	movslq	%ecx, %rcx
	movq	(%rax,%rcx,8), %rdi
	callq	atoi
	movl	%eax, -4(%rbp)
	jmp	.LBB1_9
.LBB1_5:                                #   in Loop: Header=BB1_1 Depth=1
	jmp	.LBB1_6
.LBB1_6:                                #   in Loop: Header=BB1_1 Depth=1
	jmp	.LBB1_7
.LBB1_7:                                #   in Loop: Header=BB1_1 Depth=1
	movl	-28(%rbp), %eax
	addl	$1, %eax
	movl	%eax, -28(%rbp)
	jmp	.LBB1_1
.LBB1_8:
	movl	$0, -4(%rbp)
.LBB1_9:
	movl	-4(%rbp), %eax
	addq	$32, %rsp
	popq	%rbp
	.cfi_def_cfa %rsp, 8
	retq
.Lfunc_end1:
	.size	_Z21getCmdLineArgumentIntiPPKcS0_, .Lfunc_end1-_Z21getCmdLineArgumentIntiPPKcS0_
	.cfi_endproc
                                        # -- End function
	.globl	_Z14findCudaDeviceiPPKc         # -- Begin function _Z14findCudaDeviceiPPKc
	.p2align	4, 0x90
	.type	_Z14findCudaDeviceiPPKc,@function
_Z14findCudaDeviceiPPKc:                # @_Z14findCudaDeviceiPPKc
	.cfi_startproc
# %bb.0:
	pushq	%rbp
	.cfi_def_cfa_offset 16
	.cfi_offset %rbp, -16
	movq	%rsp, %rbp
	.cfi_def_cfa_register %rbp
	subq	$800, %rsp                      # imm = 0x320
	movl	%edi, -4(%rbp)
	movq	%rsi, -16(%rbp)
	movl	$0, -20(%rbp)
# %bb.1:
	leaq	-20(%rbp), %rdi
	callq	cudaGetDeviceCount
	movl	%eax, -24(%rbp)
	cmpl	$0, -24(%rbp)
	je	.LBB2_3
# %bb.2:
	movq	stderr, %rax
	movq	%rax, -776(%rbp)                # 8-byte Spill
	movl	-24(%rbp), %edi
	callq	cudaGetErrorString
	movq	-776(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$85, %ecx
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB2_3:
	jmp	.LBB2_4
.LBB2_4:
	cmpl	$0, -20(%rbp)
	jne	.LBB2_6
# %bb.5:
	movq	stderr, %rdi
	movabsq	$.L.str.2, %rsi
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB2_6:
	movl	$0, -28(%rbp)
# %bb.7:
	movl	-28(%rbp), %edi
	callq	cudaSetDevice
	movl	%eax, -32(%rbp)
	cmpl	$0, -32(%rbp)
	je	.LBB2_9
# %bb.8:
	movq	stderr, %rax
	movq	%rax, -784(%rbp)                # 8-byte Spill
	movl	-32(%rbp), %edi
	callq	cudaGetErrorString
	movq	-784(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$94, %ecx
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB2_9:
	jmp	.LBB2_10
.LBB2_10:
	jmp	.LBB2_11
.LBB2_11:
	movl	-28(%rbp), %esi
	leaq	-760(%rbp), %rdi
	callq	cudaGetDeviceProperties
	movl	%eax, -764(%rbp)
	cmpl	$0, -764(%rbp)
	je	.LBB2_13
# %bb.12:
	movq	stderr, %rax
	movq	%rax, -792(%rbp)                # 8-byte Spill
	movl	-764(%rbp), %edi
	callq	cudaGetErrorString
	movq	-792(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$97, %ecx
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB2_13:
	jmp	.LBB2_14
.LBB2_14:
	movl	-28(%rbp), %esi
	leaq	-760(%rbp), %rdx
	movabsq	$.L.str.3, %rdi
	movb	$0, %al
	callq	printf
	movl	-28(%rbp), %eax
	addq	$800, %rsp                      # imm = 0x320
	popq	%rbp
	.cfi_def_cfa %rsp, 8
	retq
.Lfunc_end2:
	.size	_Z14findCudaDeviceiPPKc, .Lfunc_end2-_Z14findCudaDeviceiPPKc
	.cfi_endproc
                                        # -- End function
	.globl	_Z12ConstantInitPfif            # -- Begin function _Z12ConstantInitPfif
	.p2align	4, 0x90
	.type	_Z12ConstantInitPfif,@function
_Z12ConstantInitPfif:                   # @_Z12ConstantInitPfif
	.cfi_startproc
# %bb.0:
	pushq	%rbp
	.cfi_def_cfa_offset 16
	.cfi_offset %rbp, -16
	movq	%rsp, %rbp
	.cfi_def_cfa_register %rbp
	movq	%rdi, -8(%rbp)
	movl	%esi, -12(%rbp)
	movss	%xmm0, -16(%rbp)
	movl	$0, -20(%rbp)
.LBB3_1:                                # =>This Inner Loop Header: Depth=1
	movl	-20(%rbp), %eax
	cmpl	-12(%rbp), %eax
	jge	.LBB3_4
# %bb.2:                                #   in Loop: Header=BB3_1 Depth=1
	movss	-16(%rbp), %xmm0                # xmm0 = mem[0],zero,zero,zero
	movq	-8(%rbp), %rax
	movslq	-20(%rbp), %rcx
	movss	%xmm0, (%rax,%rcx,4)
# %bb.3:                                #   in Loop: Header=BB3_1 Depth=1
	movl	-20(%rbp), %eax
	addl	$1, %eax
	movl	%eax, -20(%rbp)
	jmp	.LBB3_1
.LBB3_4:
	popq	%rbp
	.cfi_def_cfa %rsp, 8
	retq
.Lfunc_end3:
	.size	_Z12ConstantInitPfif, .Lfunc_end3-_Z12ConstantInitPfif
	.cfi_endproc
                                        # -- End function
	.section	.rodata.cst4,"aM",@progbits,4
	.p2align	2                               # -- Begin function _Z14MatrixMultiplyiPPciRK4dim3S3_
.LCPI4_0:
	.long	0x3c23d70a                      # float 0.00999999977
.LCPI4_1:
	.long	0x3f800000                      # float 1
.LCPI4_2:
	.long	0x447a0000                      # float 1000
.LCPI4_6:
	.long	0xbc23d70a                      # float -0.00999999977
	.section	.rodata.cst8,"aM",@progbits,8
	.p2align	3
.LCPI4_3:
	.quad	0x3e112e0be0000000              # double 9.9999997171806853E-10
.LCPI4_4:
	.quad	0x3eb0c6f7a0b5ed8d              # double 9.9999999999999995E-7
	.section	.rodata.cst16,"aM",@progbits,16
	.p2align	4
.LCPI4_5:
	.quad	0x7fffffffffffffff              # double NaN
	.quad	0x7fffffffffffffff              # double NaN
	.text
	.globl	_Z14MatrixMultiplyiPPciRK4dim3S3_
	.p2align	4, 0x90
	.type	_Z14MatrixMultiplyiPPciRK4dim3S3_,@function
_Z14MatrixMultiplyiPPciRK4dim3S3_:      # @_Z14MatrixMultiplyiPPciRK4dim3S3_
	.cfi_startproc
# %bb.0:
	pushq	%rbp
	.cfi_def_cfa_offset 16
	.cfi_offset %rbp, -16
	movq	%rsp, %rbp
	.cfi_def_cfa_register %rbp
	subq	$864, %rsp                      # imm = 0x360
	movl	%edi, -8(%rbp)
	movq	%rsi, -16(%rbp)
	movl	%edx, -20(%rbp)
	movq	%rcx, -32(%rbp)
	movq	%r8, -40(%rbp)
	movq	-32(%rbp), %rax
	movl	(%rax), %eax
	movq	-32(%rbp), %rcx
	imull	4(%rcx), %eax
	movl	%eax, -44(%rbp)
	movl	-44(%rbp), %eax
                                        # kill: def $rax killed $eax
	shlq	$2, %rax
                                        # kill: def $eax killed $eax killed $rax
	movl	%eax, -48(%rbp)
# %bb.1:
	movl	-48(%rbp), %eax
	movl	%eax, %esi
	leaq	-56(%rbp), %rdi
	xorl	%edx, %edx
	callq	_ZL14cudaMallocHostIfE9cudaErrorPPT_mj
	movl	%eax, -60(%rbp)
	cmpl	$0, -60(%rbp)
	je	.LBB4_3
# %bb.2:
	movq	stderr, %rax
	movq	%rax, -656(%rbp)                # 8-byte Spill
	movl	-60(%rbp), %edi
	callq	cudaGetErrorString
	movq	-656(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$196, %ecx
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_3:
	jmp	.LBB4_4
.LBB4_4:
	movq	-40(%rbp), %rax
	movl	(%rax), %eax
	movq	-40(%rbp), %rcx
	imull	4(%rcx), %eax
	movl	%eax, -64(%rbp)
	movl	-64(%rbp), %eax
                                        # kill: def $rax killed $eax
	shlq	$2, %rax
                                        # kill: def $eax killed $eax killed $rax
	movl	%eax, -68(%rbp)
# %bb.5:
	movl	-68(%rbp), %eax
	movl	%eax, %esi
	leaq	-80(%rbp), %rdi
	xorl	%edx, %edx
	callq	_ZL14cudaMallocHostIfE9cudaErrorPPT_mj
	movl	%eax, -84(%rbp)
	cmpl	$0, -84(%rbp)
	je	.LBB4_7
# %bb.6:
	movq	stderr, %rax
	movq	%rax, -664(%rbp)                # 8-byte Spill
	movl	-84(%rbp), %edi
	callq	cudaGetErrorString
	movq	-664(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$200, %ecx
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_7:
	jmp	.LBB4_8
.LBB4_8:
	movss	.LCPI4_0(%rip), %xmm0           # xmm0 = mem[0],zero,zero,zero
	movss	%xmm0, -100(%rbp)
	movq	-56(%rbp), %rdi
	movl	-44(%rbp), %esi
	movss	.LCPI4_1(%rip), %xmm0           # xmm0 = mem[0],zero,zero,zero
	callq	_Z12ConstantInitPfif
	movq	-80(%rbp), %rdi
	movl	-64(%rbp), %esi
	movss	.LCPI4_0(%rip), %xmm0           # xmm0 = mem[0],zero,zero,zero
	callq	_Z12ConstantInitPfif
	movq	-40(%rbp), %rax
	movl	(%rax), %esi
	movq	-32(%rbp), %rax
	movl	4(%rax), %edx
	leaq	-144(%rbp), %rdi
	movl	$1, %ecx
	callq	_ZN4dim3C2Ejjj
	movl	-144(%rbp), %eax
	imull	-140(%rbp), %eax
	movl	%eax, %eax
                                        # kill: def $rax killed $eax
	shlq	$2, %rax
                                        # kill: def $eax killed $eax killed $rax
	movl	%eax, -148(%rbp)
# %bb.9:
	movl	-148(%rbp), %eax
	movl	%eax, %esi
	leaq	-160(%rbp), %rdi
	xorl	%edx, %edx
	callq	_ZL14cudaMallocHostIfE9cudaErrorPPT_mj
	movl	%eax, -164(%rbp)
	cmpl	$0, -164(%rbp)
	je	.LBB4_11
# %bb.10:
	movq	stderr, %rax
	movq	%rax, -672(%rbp)                # 8-byte Spill
	movl	-164(%rbp), %edi
	callq	cudaGetErrorString
	movq	-672(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$215, %ecx
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_11:
	jmp	.LBB4_12
.LBB4_12:
	cmpq	$0, -160(%rbp)
	jne	.LBB4_14
# %bb.13:
	movq	stderr, %rdi
	movabsq	$.L.str.4, %rsi
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_14:
	jmp	.LBB4_15
.LBB4_15:
	leaq	-112(%rbp), %rdi
	movl	-48(%rbp), %eax
	movl	%eax, %esi
	callq	cudaMalloc
	movl	%eax, -168(%rbp)
	cmpl	$0, -168(%rbp)
	je	.LBB4_17
# %bb.16:
	movq	stderr, %rax
	movq	%rax, -680(%rbp)                # 8-byte Spill
	movl	-168(%rbp), %edi
	callq	cudaGetErrorString
	movq	-680(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$222, %ecx
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_17:
	jmp	.LBB4_18
.LBB4_18:
	jmp	.LBB4_19
.LBB4_19:
	leaq	-120(%rbp), %rdi
	movl	-68(%rbp), %eax
	movl	%eax, %esi
	callq	cudaMalloc
	movl	%eax, -172(%rbp)
	cmpl	$0, -172(%rbp)
	je	.LBB4_21
# %bb.20:
	movq	stderr, %rax
	movq	%rax, -688(%rbp)                # 8-byte Spill
	movl	-172(%rbp), %edi
	callq	cudaGetErrorString
	movq	-688(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$223, %ecx
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_21:
	jmp	.LBB4_22
.LBB4_22:
	jmp	.LBB4_23
.LBB4_23:
	leaq	-128(%rbp), %rdi
	movl	-148(%rbp), %eax
	movl	%eax, %esi
	callq	cudaMalloc
	movl	%eax, -176(%rbp)
	cmpl	$0, -176(%rbp)
	je	.LBB4_25
# %bb.24:
	movq	stderr, %rax
	movq	%rax, -696(%rbp)                # 8-byte Spill
	movl	-176(%rbp), %edi
	callq	cudaGetErrorString
	movq	-696(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$224, %ecx
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_25:
	jmp	.LBB4_26
.LBB4_26:
	jmp	.LBB4_27
.LBB4_27:
	leaq	-184(%rbp), %rdi
	callq	cudaEventCreate
	movl	%eax, -196(%rbp)
	cmpl	$0, -196(%rbp)
	je	.LBB4_29
# %bb.28:
	movq	stderr, %rax
	movq	%rax, -704(%rbp)                # 8-byte Spill
	movl	-196(%rbp), %edi
	callq	cudaGetErrorString
	movq	-704(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$227, %ecx
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_29:
	jmp	.LBB4_30
.LBB4_30:
	jmp	.LBB4_31
.LBB4_31:
	leaq	-192(%rbp), %rdi
	callq	cudaEventCreate
	movl	%eax, -200(%rbp)
	cmpl	$0, -200(%rbp)
	je	.LBB4_33
# %bb.32:
	movq	stderr, %rax
	movq	%rax, -712(%rbp)                # 8-byte Spill
	movl	-200(%rbp), %edi
	callq	cudaGetErrorString
	movq	-712(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$228, %ecx
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_33:
	jmp	.LBB4_34
.LBB4_34:
	jmp	.LBB4_35
.LBB4_35:
	leaq	-96(%rbp), %rdi
	movl	$1, %esi
	callq	cudaStreamCreateWithFlags
	movl	%eax, -204(%rbp)
	cmpl	$0, -204(%rbp)
	je	.LBB4_37
# %bb.36:
	movq	stderr, %rax
	movq	%rax, -720(%rbp)                # 8-byte Spill
	movl	-204(%rbp), %edi
	callq	cudaGetErrorString
	movq	-720(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$230, %ecx
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_37:
	jmp	.LBB4_38
.LBB4_38:
	jmp	.LBB4_39
.LBB4_39:
	movq	-112(%rbp), %rdi
	movq	-56(%rbp), %rsi
	movl	-48(%rbp), %eax
	movl	%eax, %edx
	movq	-96(%rbp), %r8
	movl	$1, %ecx
	callq	cudaMemcpyAsync
	movl	%eax, -208(%rbp)
	cmpl	$0, -208(%rbp)
	je	.LBB4_41
# %bb.40:
	movq	stderr, %rax
	movq	%rax, -728(%rbp)                # 8-byte Spill
	movl	-208(%rbp), %edi
	callq	cudaGetErrorString
	movq	-728(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$234, %ecx
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_41:
	jmp	.LBB4_42
.LBB4_42:
	jmp	.LBB4_43
.LBB4_43:
	movq	-120(%rbp), %rdi
	movq	-80(%rbp), %rsi
	movl	-68(%rbp), %eax
	movl	%eax, %edx
	movq	-96(%rbp), %r8
	movl	$1, %ecx
	callq	cudaMemcpyAsync
	movl	%eax, -212(%rbp)
	cmpl	$0, -212(%rbp)
	je	.LBB4_45
# %bb.44:
	movq	stderr, %rax
	movq	%rax, -736(%rbp)                # 8-byte Spill
	movl	-212(%rbp), %edi
	callq	cudaGetErrorString
	movq	-736(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$236, %ecx
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_45:
	jmp	.LBB4_46
.LBB4_46:
	movl	-20(%rbp), %esi
	movl	-20(%rbp), %edx
	leaq	-224(%rbp), %rdi
	movl	$1, %ecx
	callq	_ZN4dim3C2Ejjj
	movq	-40(%rbp), %rax
	movl	(%rax), %eax
	xorl	%edx, %edx
	divl	-224(%rbp)
	movl	%eax, %esi
	movq	-32(%rbp), %rax
	movl	4(%rax), %eax
	xorl	%edx, %edx
	divl	-220(%rbp)
	movl	%eax, %edx
	leaq	-240(%rbp), %rdi
	movl	$1, %ecx
	callq	_ZN4dim3C2Ejjj
	movabsq	$.L.str.5, %rdi
	movb	$0, %al
	callq	printf
	cmpl	$16, -20(%rbp)
	jne	.LBB4_50
# %bb.47:
	movq	-240(%rbp), %rax
	movq	%rax, -256(%rbp)
	movl	-232(%rbp), %eax
	movl	%eax, -248(%rbp)
	movq	-224(%rbp), %rax
	movq	%rax, -272(%rbp)
	movl	-216(%rbp), %eax
	movl	%eax, -264(%rbp)
	movq	-96(%rbp), %r9
	movq	-256(%rbp), %rax
	movq	%rax, -288(%rbp)
	movl	-248(%rbp), %eax
	movl	%eax, -280(%rbp)
	movq	-288(%rbp), %rdi
	movl	-280(%rbp), %esi
	movq	-272(%rbp), %rax
	movq	%rax, -304(%rbp)
	movl	-264(%rbp), %eax
	movl	%eax, -296(%rbp)
	movq	-304(%rbp), %rdx
	movl	-296(%rbp), %ecx
	xorl	%eax, %eax
	movl	%eax, %r8d
	callq	__cudaPushCallConfiguration
	cmpl	$0, %eax
	jne	.LBB4_49
# %bb.48:
	movq	-128(%rbp), %rdi
	movq	-112(%rbp), %rsi
	movq	-120(%rbp), %rdx
	movq	-32(%rbp), %rax
	movl	(%rax), %ecx
	movq	-40(%rbp), %rax
	movl	(%rax), %r8d
	callq	_Z28__device_stub__MatrixMulCUDAILi16EEvPfS0_S0_ii
.LBB4_49:
	jmp	.LBB4_53
.LBB4_50:
	movq	-240(%rbp), %rax
	movq	%rax, -320(%rbp)
	movl	-232(%rbp), %eax
	movl	%eax, -312(%rbp)
	movq	-224(%rbp), %rax
	movq	%rax, -336(%rbp)
	movl	-216(%rbp), %eax
	movl	%eax, -328(%rbp)
	movq	-96(%rbp), %r9
	movq	-320(%rbp), %rax
	movq	%rax, -352(%rbp)
	movl	-312(%rbp), %eax
	movl	%eax, -344(%rbp)
	movq	-352(%rbp), %rdi
	movl	-344(%rbp), %esi
	movq	-336(%rbp), %rax
	movq	%rax, -368(%rbp)
	movl	-328(%rbp), %eax
	movl	%eax, -360(%rbp)
	movq	-368(%rbp), %rdx
	movl	-360(%rbp), %ecx
	xorl	%eax, %eax
	movl	%eax, %r8d
	callq	__cudaPushCallConfiguration
	cmpl	$0, %eax
	jne	.LBB4_52
# %bb.51:
	movq	-128(%rbp), %rdi
	movq	-112(%rbp), %rsi
	movq	-120(%rbp), %rdx
	movq	-32(%rbp), %rax
	movl	(%rax), %ecx
	movq	-40(%rbp), %rax
	movl	(%rax), %r8d
	callq	_Z28__device_stub__MatrixMulCUDAILi32EEvPfS0_S0_ii
.LBB4_52:
	jmp	.LBB4_53
.LBB4_53:
	movabsq	$.L.str.6, %rdi
	movb	$0, %al
	callq	printf
# %bb.54:
	movq	-96(%rbp), %rdi
	callq	cudaStreamSynchronize
	movl	%eax, -372(%rbp)
	cmpl	$0, -372(%rbp)
	je	.LBB4_56
# %bb.55:
	movq	stderr, %rax
	movq	%rax, -744(%rbp)                # 8-byte Spill
	movl	-372(%rbp), %edi
	callq	cudaGetErrorString
	movq	-744(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$255, %ecx
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_56:
	jmp	.LBB4_57
.LBB4_57:
	jmp	.LBB4_58
.LBB4_58:
	movq	-184(%rbp), %rdi
	movq	-96(%rbp), %rsi
	callq	cudaEventRecord
	movl	%eax, -376(%rbp)
	cmpl	$0, -376(%rbp)
	je	.LBB4_60
# %bb.59:
	movq	stderr, %rax
	movq	%rax, -752(%rbp)                # 8-byte Spill
	movl	-376(%rbp), %edi
	callq	cudaGetErrorString
	movq	-752(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$258, %ecx                      # imm = 0x102
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_60:
	jmp	.LBB4_61
.LBB4_61:
	movl	$300, -380(%rbp)                # imm = 0x12C
	movl	$0, -384(%rbp)
.LBB4_62:                               # =>This Inner Loop Header: Depth=1
	movl	-384(%rbp), %eax
	cmpl	-380(%rbp), %eax
	jge	.LBB4_72
# %bb.63:                               #   in Loop: Header=BB4_62 Depth=1
	cmpl	$16, -20(%rbp)
	jne	.LBB4_67
# %bb.64:                               #   in Loop: Header=BB4_62 Depth=1
	movq	-240(%rbp), %rax
	movq	%rax, -400(%rbp)
	movl	-232(%rbp), %eax
	movl	%eax, -392(%rbp)
	movq	-224(%rbp), %rax
	movq	%rax, -416(%rbp)
	movl	-216(%rbp), %eax
	movl	%eax, -408(%rbp)
	movq	-96(%rbp), %r9
	movq	-400(%rbp), %rax
	movq	%rax, -432(%rbp)
	movl	-392(%rbp), %eax
	movl	%eax, -424(%rbp)
	movq	-432(%rbp), %rdi
	movl	-424(%rbp), %esi
	movq	-416(%rbp), %rax
	movq	%rax, -448(%rbp)
	movl	-408(%rbp), %eax
	movl	%eax, -440(%rbp)
	movq	-448(%rbp), %rdx
	movl	-440(%rbp), %ecx
	xorl	%eax, %eax
	movl	%eax, %r8d
	callq	__cudaPushCallConfiguration
	cmpl	$0, %eax
	jne	.LBB4_66
# %bb.65:                               #   in Loop: Header=BB4_62 Depth=1
	movq	-128(%rbp), %rdi
	movq	-112(%rbp), %rsi
	movq	-120(%rbp), %rdx
	movq	-32(%rbp), %rax
	movl	(%rax), %ecx
	movq	-40(%rbp), %rax
	movl	(%rax), %r8d
	callq	_Z28__device_stub__MatrixMulCUDAILi16EEvPfS0_S0_ii
.LBB4_66:                               #   in Loop: Header=BB4_62 Depth=1
	jmp	.LBB4_70
.LBB4_67:                               #   in Loop: Header=BB4_62 Depth=1
	movq	-240(%rbp), %rax
	movq	%rax, -464(%rbp)
	movl	-232(%rbp), %eax
	movl	%eax, -456(%rbp)
	movq	-224(%rbp), %rax
	movq	%rax, -480(%rbp)
	movl	-216(%rbp), %eax
	movl	%eax, -472(%rbp)
	movq	-96(%rbp), %r9
	movq	-464(%rbp), %rax
	movq	%rax, -496(%rbp)
	movl	-456(%rbp), %eax
	movl	%eax, -488(%rbp)
	movq	-496(%rbp), %rdi
	movl	-488(%rbp), %esi
	movq	-480(%rbp), %rax
	movq	%rax, -512(%rbp)
	movl	-472(%rbp), %eax
	movl	%eax, -504(%rbp)
	movq	-512(%rbp), %rdx
	movl	-504(%rbp), %ecx
	xorl	%eax, %eax
	movl	%eax, %r8d
	callq	__cudaPushCallConfiguration
	cmpl	$0, %eax
	jne	.LBB4_69
# %bb.68:                               #   in Loop: Header=BB4_62 Depth=1
	movq	-128(%rbp), %rdi
	movq	-112(%rbp), %rsi
	movq	-120(%rbp), %rdx
	movq	-32(%rbp), %rax
	movl	(%rax), %ecx
	movq	-40(%rbp), %rax
	movl	(%rax), %r8d
	callq	_Z28__device_stub__MatrixMulCUDAILi32EEvPfS0_S0_ii
.LBB4_69:                               #   in Loop: Header=BB4_62 Depth=1
	jmp	.LBB4_70
.LBB4_70:                               #   in Loop: Header=BB4_62 Depth=1
	jmp	.LBB4_71
.LBB4_71:                               #   in Loop: Header=BB4_62 Depth=1
	movl	-384(%rbp), %eax
	addl	$1, %eax
	movl	%eax, -384(%rbp)
	jmp	.LBB4_62
.LBB4_72:
	jmp	.LBB4_73
.LBB4_73:
	movq	-192(%rbp), %rdi
	movq	-96(%rbp), %rsi
	callq	cudaEventRecord
	movl	%eax, -516(%rbp)
	cmpl	$0, -516(%rbp)
	je	.LBB4_75
# %bb.74:
	movq	stderr, %rax
	movq	%rax, -760(%rbp)                # 8-byte Spill
	movl	-516(%rbp), %edi
	callq	cudaGetErrorString
	movq	-760(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$274, %ecx                      # imm = 0x112
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_75:
	jmp	.LBB4_76
.LBB4_76:
	jmp	.LBB4_77
.LBB4_77:
	movq	-192(%rbp), %rdi
	callq	cudaEventSynchronize
	movl	%eax, -520(%rbp)
	cmpl	$0, -520(%rbp)
	je	.LBB4_79
# %bb.78:
	movq	stderr, %rax
	movq	%rax, -768(%rbp)                # 8-byte Spill
	movl	-520(%rbp), %edi
	callq	cudaGetErrorString
	movq	-768(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$277, %ecx                      # imm = 0x115
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_79:
	jmp	.LBB4_80
.LBB4_80:
	xorps	%xmm0, %xmm0
	movss	%xmm0, -524(%rbp)
# %bb.81:
	movq	-184(%rbp), %rsi
	movq	-192(%rbp), %rdx
	leaq	-524(%rbp), %rdi
	callq	cudaEventElapsedTime
	movl	%eax, -528(%rbp)
	cmpl	$0, -528(%rbp)
	je	.LBB4_83
# %bb.82:
	movq	stderr, %rax
	movq	%rax, -776(%rbp)                # 8-byte Spill
	movl	-528(%rbp), %edi
	callq	cudaGetErrorString
	movq	-776(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$280, %ecx                      # imm = 0x118
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_83:
	jmp	.LBB4_84
.LBB4_84:
	movss	-524(%rbp), %xmm0               # xmm0 = mem[0],zero,zero,zero
	cvtsi2ssl	-380(%rbp), %xmm1
	divss	%xmm1, %xmm0
	movss	%xmm0, -532(%rbp)
	movq	-32(%rbp), %rax
	movl	(%rax), %ecx
	movl	4(%rax), %eax
                                        # kill: def $rcx killed $ecx
	cvtsi2sd	%rcx, %xmm0
	addsd	%xmm0, %xmm0
                                        # kill: def $rax killed $eax
	cvtsi2sd	%rax, %xmm1
	mulsd	%xmm1, %xmm0
	movq	-40(%rbp), %rax
	movl	(%rax), %eax
                                        # kill: def $rax killed $eax
	cvtsi2sd	%rax, %xmm1
	mulsd	%xmm1, %xmm0
	movsd	%xmm0, -544(%rbp)
	movsd	.LCPI4_3(%rip), %xmm0           # xmm0 = mem[0],zero
	mulsd	-544(%rbp), %xmm0
	movss	-532(%rbp), %xmm1               # xmm1 = mem[0],zero,zero,zero
	movss	.LCPI4_2(%rip), %xmm2           # xmm2 = mem[0],zero,zero,zero
	divss	%xmm2, %xmm1
	cvtss2sd	%xmm1, %xmm1
	divsd	%xmm1, %xmm0
	movsd	%xmm0, -552(%rbp)
	movsd	-552(%rbp), %xmm0               # xmm0 = mem[0],zero
	movss	-532(%rbp), %xmm1               # xmm1 = mem[0],zero,zero,zero
	cvtss2sd	%xmm1, %xmm1
	movsd	-544(%rbp), %xmm2               # xmm2 = mem[0],zero
	movl	-224(%rbp), %esi
	imull	-220(%rbp), %esi
	movabsq	$.L.str.7, %rdi
	movb	$3, %al
	callq	printf
# %bb.85:
	movq	-160(%rbp), %rdi
	movq	-128(%rbp), %rsi
	movl	-148(%rbp), %eax
	movl	%eax, %edx
	movq	-96(%rbp), %r8
	movl	$2, %ecx
	callq	cudaMemcpyAsync
	movl	%eax, -556(%rbp)
	cmpl	$0, -556(%rbp)
	je	.LBB4_87
# %bb.86:
	movq	stderr, %rax
	movq	%rax, -784(%rbp)                # 8-byte Spill
	movl	-556(%rbp), %edi
	callq	cudaGetErrorString
	movq	-784(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$296, %ecx                      # imm = 0x128
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_87:
	jmp	.LBB4_88
.LBB4_88:
	jmp	.LBB4_89
.LBB4_89:
	movq	-96(%rbp), %rdi
	callq	cudaStreamSynchronize
	movl	%eax, -560(%rbp)
	cmpl	$0, -560(%rbp)
	je	.LBB4_91
# %bb.90:
	movq	stderr, %rax
	movq	%rax, -792(%rbp)                # 8-byte Spill
	movl	-560(%rbp), %edi
	callq	cudaGetErrorString
	movq	-792(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$297, %ecx                      # imm = 0x129
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_91:
	jmp	.LBB4_92
.LBB4_92:
	movabsq	$.L.str.8, %rdi
	movb	$0, %al
	callq	printf
	movb	$1, -561(%rbp)
	movsd	.LCPI4_4(%rip), %xmm0           # xmm0 = mem[0],zero
	movsd	%xmm0, -576(%rbp)
	movl	$0, -580(%rbp)
.LBB4_93:                               # =>This Inner Loop Header: Depth=1
	movl	-580(%rbp), %eax
	movl	-144(%rbp), %ecx
	imull	-140(%rbp), %ecx
	cmpl	%ecx, %eax
	jge	.LBB4_98
# %bb.94:                               #   in Loop: Header=BB4_93 Depth=1
	movq	-160(%rbp), %rax
	movslq	-580(%rbp), %rcx
	movss	(%rax,%rcx,4), %xmm0            # xmm0 = mem[0],zero,zero,zero
	movq	-32(%rbp), %rax
	movl	(%rax), %eax
                                        # kill: def $rax killed $eax
	cvtsi2ss	%rax, %xmm1
	movss	.LCPI4_6(%rip), %xmm2           # xmm2 = mem[0],zero,zero,zero
	mulss	%xmm2, %xmm1
	addss	%xmm1, %xmm0
	cvtss2sd	%xmm0, %xmm0
	movaps	.LCPI4_5(%rip), %xmm1           # xmm1 = [NaN,NaN]
	pand	%xmm1, %xmm0
	movlpd	%xmm0, -592(%rbp)
	movq	-32(%rbp), %rax
	movl	(%rax), %eax
                                        # kill: def $rax killed $eax
	cvtsi2sd	%rax, %xmm0
	movsd	%xmm0, -600(%rbp)
	movq	-160(%rbp), %rax
	movslq	-580(%rbp), %rcx
	movss	(%rax,%rcx,4), %xmm0            # xmm0 = mem[0],zero,zero,zero
	cvtss2sd	%xmm0, %xmm0
	movaps	.LCPI4_5(%rip), %xmm1           # xmm1 = [NaN,NaN]
	pand	%xmm1, %xmm0
	movsd	%xmm0, -608(%rbp)
	movsd	-592(%rbp), %xmm0               # xmm0 = mem[0],zero
	divsd	-608(%rbp), %xmm0
	divsd	-600(%rbp), %xmm0
	movsd	%xmm0, -616(%rbp)
	movsd	-616(%rbp), %xmm0               # xmm0 = mem[0],zero
	ucomisd	-576(%rbp), %xmm0
	jbe	.LBB4_96
# %bb.95:                               #   in Loop: Header=BB4_93 Depth=1
	movslq	-580(%rbp), %rcx
	movl	%ecx, %esi
	movq	-160(%rbp), %rax
	movss	(%rax,%rcx,4), %xmm0            # xmm0 = mem[0],zero,zero,zero
	cvtss2sd	%xmm0, %xmm0
	movq	-32(%rbp), %rax
	movl	(%rax), %eax
                                        # kill: def $rax killed $eax
	cvtsi2ss	%rax, %xmm1
	movss	.LCPI4_0(%rip), %xmm2           # xmm2 = mem[0],zero,zero,zero
	mulss	%xmm2, %xmm1
	cvtss2sd	%xmm1, %xmm1
	movsd	-576(%rbp), %xmm2               # xmm2 = mem[0],zero
	movabsq	$.L.str.9, %rdi
	movb	$3, %al
	callq	printf
	movb	$0, -561(%rbp)
.LBB4_96:                               #   in Loop: Header=BB4_93 Depth=1
	jmp	.LBB4_97
.LBB4_97:                               #   in Loop: Header=BB4_93 Depth=1
	movl	-580(%rbp), %eax
	addl	$1, %eax
	movl	%eax, -580(%rbp)
	jmp	.LBB4_93
.LBB4_98:
	testb	$1, -561(%rbp)
	je	.LBB4_100
# %bb.99:
	movabsq	$.L.str.11, %rax
	movq	%rax, -800(%rbp)                # 8-byte Spill
	jmp	.LBB4_101
.LBB4_100:
	movabsq	$.L.str.12, %rax
	movq	%rax, -800(%rbp)                # 8-byte Spill
	jmp	.LBB4_101
.LBB4_101:
	movq	-800(%rbp), %rsi                # 8-byte Reload
	movabsq	$.L.str.10, %rdi
	movb	$0, %al
	callq	printf
# %bb.102:
	movq	-56(%rbp), %rdi
	callq	cudaFreeHost
	movl	%eax, -620(%rbp)
	cmpl	$0, -620(%rbp)
	je	.LBB4_104
# %bb.103:
	movq	stderr, %rax
	movq	%rax, -808(%rbp)                # 8-byte Spill
	movl	-620(%rbp), %edi
	callq	cudaGetErrorString
	movq	-808(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$322, %ecx                      # imm = 0x142
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_104:
	jmp	.LBB4_105
.LBB4_105:
	jmp	.LBB4_106
.LBB4_106:
	movq	-80(%rbp), %rdi
	callq	cudaFreeHost
	movl	%eax, -624(%rbp)
	cmpl	$0, -624(%rbp)
	je	.LBB4_108
# %bb.107:
	movq	stderr, %rax
	movq	%rax, -816(%rbp)                # 8-byte Spill
	movl	-624(%rbp), %edi
	callq	cudaGetErrorString
	movq	-816(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$323, %ecx                      # imm = 0x143
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_108:
	jmp	.LBB4_109
.LBB4_109:
	jmp	.LBB4_110
.LBB4_110:
	movq	-160(%rbp), %rdi
	callq	cudaFreeHost
	movl	%eax, -628(%rbp)
	cmpl	$0, -628(%rbp)
	je	.LBB4_112
# %bb.111:
	movq	stderr, %rax
	movq	%rax, -824(%rbp)                # 8-byte Spill
	movl	-628(%rbp), %edi
	callq	cudaGetErrorString
	movq	-824(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$324, %ecx                      # imm = 0x144
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_112:
	jmp	.LBB4_113
.LBB4_113:
	jmp	.LBB4_114
.LBB4_114:
	movq	-112(%rbp), %rdi
	callq	cudaFree
	movl	%eax, -632(%rbp)
	cmpl	$0, -632(%rbp)
	je	.LBB4_116
# %bb.115:
	movq	stderr, %rax
	movq	%rax, -832(%rbp)                # 8-byte Spill
	movl	-632(%rbp), %edi
	callq	cudaGetErrorString
	movq	-832(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$325, %ecx                      # imm = 0x145
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_116:
	jmp	.LBB4_117
.LBB4_117:
	jmp	.LBB4_118
.LBB4_118:
	movq	-120(%rbp), %rdi
	callq	cudaFree
	movl	%eax, -636(%rbp)
	cmpl	$0, -636(%rbp)
	je	.LBB4_120
# %bb.119:
	movq	stderr, %rax
	movq	%rax, -840(%rbp)                # 8-byte Spill
	movl	-636(%rbp), %edi
	callq	cudaGetErrorString
	movq	-840(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$326, %ecx                      # imm = 0x146
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_120:
	jmp	.LBB4_121
.LBB4_121:
	jmp	.LBB4_122
.LBB4_122:
	movq	-128(%rbp), %rdi
	callq	cudaFree
	movl	%eax, -640(%rbp)
	cmpl	$0, -640(%rbp)
	je	.LBB4_124
# %bb.123:
	movq	stderr, %rax
	movq	%rax, -848(%rbp)                # 8-byte Spill
	movl	-640(%rbp), %edi
	callq	cudaGetErrorString
	movq	-848(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$327, %ecx                      # imm = 0x147
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_124:
	jmp	.LBB4_125
.LBB4_125:
	jmp	.LBB4_126
.LBB4_126:
	movq	-184(%rbp), %rdi
	callq	cudaEventDestroy
	movl	%eax, -644(%rbp)
	cmpl	$0, -644(%rbp)
	je	.LBB4_128
# %bb.127:
	movq	stderr, %rax
	movq	%rax, -856(%rbp)                # 8-byte Spill
	movl	-644(%rbp), %edi
	callq	cudaGetErrorString
	movq	-856(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$328, %ecx                      # imm = 0x148
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_128:
	jmp	.LBB4_129
.LBB4_129:
	jmp	.LBB4_130
.LBB4_130:
	movq	-192(%rbp), %rdi
	callq	cudaEventDestroy
	movl	%eax, -648(%rbp)
	cmpl	$0, -648(%rbp)
	je	.LBB4_132
# %bb.131:
	movq	stderr, %rax
	movq	%rax, -864(%rbp)                # 8-byte Spill
	movl	-648(%rbp), %edi
	callq	cudaGetErrorString
	movq	-864(%rbp), %rdi                # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$329, %ecx                      # imm = 0x149
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB4_132:
	jmp	.LBB4_133
.LBB4_133:
	movabsq	$.L.str.13, %rdi
	movb	$0, %al
	callq	printf
	testb	$1, -561(%rbp)
	je	.LBB4_135
# %bb.134:
	movl	$0, -4(%rbp)
	jmp	.LBB4_136
.LBB4_135:
	movl	$1, -4(%rbp)
.LBB4_136:
	movl	-4(%rbp), %eax
	addq	$864, %rsp                      # imm = 0x360
	popq	%rbp
	.cfi_def_cfa %rsp, 8
	retq
.Lfunc_end4:
	.size	_Z14MatrixMultiplyiPPciRK4dim3S3_, .Lfunc_end4-_Z14MatrixMultiplyiPPciRK4dim3S3_
	.cfi_endproc
                                        # -- End function
	.p2align	4, 0x90                         # -- Begin function _ZL14cudaMallocHostIfE9cudaErrorPPT_mj
	.type	_ZL14cudaMallocHostIfE9cudaErrorPPT_mj,@function
_ZL14cudaMallocHostIfE9cudaErrorPPT_mj: # @_ZL14cudaMallocHostIfE9cudaErrorPPT_mj
	.cfi_startproc
# %bb.0:
	pushq	%rbp
	.cfi_def_cfa_offset 16
	.cfi_offset %rbp, -16
	movq	%rsp, %rbp
	.cfi_def_cfa_register %rbp
	subq	$32, %rsp
	movq	%rdi, -8(%rbp)
	movq	%rsi, -16(%rbp)
	movl	%edx, -20(%rbp)
	movq	-8(%rbp), %rdi
	movq	-16(%rbp), %rsi
	movl	-20(%rbp), %edx
	callq	_ZL14cudaMallocHostPPvmj
	addq	$32, %rsp
	popq	%rbp
	.cfi_def_cfa %rsp, 8
	retq
.Lfunc_end5:
	.size	_ZL14cudaMallocHostIfE9cudaErrorPPT_mj, .Lfunc_end5-_ZL14cudaMallocHostIfE9cudaErrorPPT_mj
	.cfi_endproc
                                        # -- End function
	.section	.text._ZN4dim3C2Ejjj,"axG",@progbits,_ZN4dim3C2Ejjj,comdat
	.weak	_ZN4dim3C2Ejjj                  # -- Begin function _ZN4dim3C2Ejjj
	.p2align	4, 0x90
	.type	_ZN4dim3C2Ejjj,@function
_ZN4dim3C2Ejjj:                         # @_ZN4dim3C2Ejjj
	.cfi_startproc
# %bb.0:
	pushq	%rbp
	.cfi_def_cfa_offset 16
	.cfi_offset %rbp, -16
	movq	%rsp, %rbp
	.cfi_def_cfa_register %rbp
	movq	%rdi, -8(%rbp)
	movl	%esi, -12(%rbp)
	movl	%edx, -16(%rbp)
	movl	%ecx, -20(%rbp)
	movq	-8(%rbp), %rax
	movl	-12(%rbp), %ecx
	movl	%ecx, (%rax)
	movl	-16(%rbp), %ecx
	movl	%ecx, 4(%rax)
	movl	-20(%rbp), %ecx
	movl	%ecx, 8(%rax)
	popq	%rbp
	.cfi_def_cfa %rsp, 8
	retq
.Lfunc_end6:
	.size	_ZN4dim3C2Ejjj, .Lfunc_end6-_ZN4dim3C2Ejjj
	.cfi_endproc
                                        # -- End function
	.section	.text._Z28__device_stub__MatrixMulCUDAILi16EEvPfS0_S0_ii,"axG",@progbits,_Z28__device_stub__MatrixMulCUDAILi16EEvPfS0_S0_ii,comdat
	.weak	_Z28__device_stub__MatrixMulCUDAILi16EEvPfS0_S0_ii # -- Begin function _Z28__device_stub__MatrixMulCUDAILi16EEvPfS0_S0_ii
	.p2align	4, 0x90
	.type	_Z28__device_stub__MatrixMulCUDAILi16EEvPfS0_S0_ii,@function
_Z28__device_stub__MatrixMulCUDAILi16EEvPfS0_S0_ii: # @_Z28__device_stub__MatrixMulCUDAILi16EEvPfS0_S0_ii
	.cfi_startproc
# %bb.0:
	pushq	%rbp
	.cfi_def_cfa_offset 16
	.cfi_offset %rbp, -16
	movq	%rsp, %rbp
	.cfi_def_cfa_register %rbp
	subq	$176, %rsp
	movq	%rdi, -8(%rbp)
	movq	%rsi, -16(%rbp)
	movq	%rdx, -24(%rbp)
	movl	%ecx, -28(%rbp)
	movl	%r8d, -32(%rbp)
	leaq	-8(%rbp), %rax
	movq	%rax, -160(%rbp)
	leaq	-16(%rbp), %rax
	movq	%rax, -152(%rbp)
	leaq	-24(%rbp), %rax
	movq	%rax, -144(%rbp)
	leaq	-28(%rbp), %rax
	movq	%rax, -136(%rbp)
	leaq	-32(%rbp), %rax
	movq	%rax, -128(%rbp)
	leaq	-48(%rbp), %rdi
	leaq	-64(%rbp), %rsi
	leaq	-72(%rbp), %rdx
	leaq	-80(%rbp), %rcx
	callq	__cudaPopCallConfiguration
	movq	-72(%rbp), %r10
	movq	-80(%rbp), %rax
	movq	-48(%rbp), %rcx
	movq	%rcx, -96(%rbp)
	movl	-40(%rbp), %ecx
	movl	%ecx, -88(%rbp)
	movq	-96(%rbp), %rsi
	movl	-88(%rbp), %edx
	movq	-64(%rbp), %rcx
	movq	%rcx, -112(%rbp)
	movl	-56(%rbp), %ecx
	movl	%ecx, -104(%rbp)
	movq	-112(%rbp), %rcx
	movl	-104(%rbp), %r8d
	movabsq	$_Z28__device_stub__MatrixMulCUDAILi16EEvPfS0_S0_ii, %rdi
	leaq	-160(%rbp), %r9
	movq	%r10, (%rsp)
	movq	%rax, 8(%rsp)
	callq	cudaLaunchKernel
# %bb.1:
	addq	$176, %rsp
	popq	%rbp
	.cfi_def_cfa %rsp, 8
	retq
.Lfunc_end7:
	.size	_Z28__device_stub__MatrixMulCUDAILi16EEvPfS0_S0_ii, .Lfunc_end7-_Z28__device_stub__MatrixMulCUDAILi16EEvPfS0_S0_ii
	.cfi_endproc
                                        # -- End function
	.section	.text._Z28__device_stub__MatrixMulCUDAILi32EEvPfS0_S0_ii,"axG",@progbits,_Z28__device_stub__MatrixMulCUDAILi32EEvPfS0_S0_ii,comdat
	.weak	_Z28__device_stub__MatrixMulCUDAILi32EEvPfS0_S0_ii # -- Begin function _Z28__device_stub__MatrixMulCUDAILi32EEvPfS0_S0_ii
	.p2align	4, 0x90
	.type	_Z28__device_stub__MatrixMulCUDAILi32EEvPfS0_S0_ii,@function
_Z28__device_stub__MatrixMulCUDAILi32EEvPfS0_S0_ii: # @_Z28__device_stub__MatrixMulCUDAILi32EEvPfS0_S0_ii
	.cfi_startproc
# %bb.0:
	pushq	%rbp
	.cfi_def_cfa_offset 16
	.cfi_offset %rbp, -16
	movq	%rsp, %rbp
	.cfi_def_cfa_register %rbp
	subq	$176, %rsp
	movq	%rdi, -8(%rbp)
	movq	%rsi, -16(%rbp)
	movq	%rdx, -24(%rbp)
	movl	%ecx, -28(%rbp)
	movl	%r8d, -32(%rbp)
	leaq	-8(%rbp), %rax
	movq	%rax, -160(%rbp)
	leaq	-16(%rbp), %rax
	movq	%rax, -152(%rbp)
	leaq	-24(%rbp), %rax
	movq	%rax, -144(%rbp)
	leaq	-28(%rbp), %rax
	movq	%rax, -136(%rbp)
	leaq	-32(%rbp), %rax
	movq	%rax, -128(%rbp)
	leaq	-48(%rbp), %rdi
	leaq	-64(%rbp), %rsi
	leaq	-72(%rbp), %rdx
	leaq	-80(%rbp), %rcx
	callq	__cudaPopCallConfiguration
	movq	-72(%rbp), %r10
	movq	-80(%rbp), %rax
	movq	-48(%rbp), %rcx
	movq	%rcx, -96(%rbp)
	movl	-40(%rbp), %ecx
	movl	%ecx, -88(%rbp)
	movq	-96(%rbp), %rsi
	movl	-88(%rbp), %edx
	movq	-64(%rbp), %rcx
	movq	%rcx, -112(%rbp)
	movl	-56(%rbp), %ecx
	movl	%ecx, -104(%rbp)
	movq	-112(%rbp), %rcx
	movl	-104(%rbp), %r8d
	movabsq	$_Z28__device_stub__MatrixMulCUDAILi32EEvPfS0_S0_ii, %rdi
	leaq	-160(%rbp), %r9
	movq	%r10, (%rsp)
	movq	%rax, 8(%rsp)
	callq	cudaLaunchKernel
# %bb.1:
	addq	$176, %rsp
	popq	%rbp
	.cfi_def_cfa %rsp, 8
	retq
.Lfunc_end8:
	.size	_Z28__device_stub__MatrixMulCUDAILi32EEvPfS0_S0_ii, .Lfunc_end8-_Z28__device_stub__MatrixMulCUDAILi32EEvPfS0_S0_ii
	.cfi_endproc
                                        # -- End function
	.text
	.globl	main                            # -- Begin function main
	.p2align	4, 0x90
	.type	main,@function
main:                                   # @main
	.cfi_startproc
# %bb.0:
	pushq	%rbp
	.cfi_def_cfa_offset 16
	.cfi_offset %rbp, -16
	movq	%rsp, %rbp
	.cfi_def_cfa_register %rbp
	subq	$96, %rsp
	movl	$0, -4(%rbp)
	movl	%edi, -8(%rbp)
	movq	%rsi, -16(%rbp)
	movabsq	$.L.str.14, %rdi
	movb	$0, %al
	callq	printf
	movl	-8(%rbp), %edi
	movq	-16(%rbp), %rsi
	movabsq	$.L.str.15, %rdx
	callq	_Z16checkCmdLineFlagiPPKcS0_
	testb	$1, %al
	jne	.LBB9_2
# %bb.1:
	movl	-8(%rbp), %edi
	movq	-16(%rbp), %rsi
	movabsq	$.L.str.16, %rdx
	callq	_Z16checkCmdLineFlagiPPKcS0_
	testb	$1, %al
	jne	.LBB9_2
	jmp	.LBB9_3
.LBB9_2:
	movabsq	$.L.str.17, %rdi
	movb	$0, %al
	callq	printf
	movabsq	$.L.str.18, %rdi
	movb	$0, %al
	callq	printf
	movabsq	$.L.str.19, %rdi
	movb	$0, %al
	callq	printf
	movabsq	$.L.str.20, %rdi
	movb	$0, %al
	callq	printf
	xorl	%edi, %edi
	callq	exit
.LBB9_3:
	movl	-8(%rbp), %edi
	movq	-16(%rbp), %rsi
	callq	_Z14findCudaDeviceiPPKc
	movl	%eax, -20(%rbp)
	movl	$32, -24(%rbp)
	imull	$10, -24(%rbp), %esi
	imull	$10, -24(%rbp), %edx
	leaq	-40(%rbp), %rdi
	movl	$1, %ecx
	callq	_ZN4dim3C2Ejjj
	imull	$20, -24(%rbp), %esi
	imull	$10, -24(%rbp), %edx
	leaq	-56(%rbp), %rdi
	movl	$1, %ecx
	callq	_ZN4dim3C2Ejjj
	movl	-8(%rbp), %edi
	movq	-16(%rbp), %rsi
	movabsq	$.L.str.21, %rdx
	callq	_Z16checkCmdLineFlagiPPKcS0_
	testb	$1, %al
	jne	.LBB9_4
	jmp	.LBB9_5
.LBB9_4:
	movl	-8(%rbp), %edi
	movq	-16(%rbp), %rsi
	movabsq	$.L.str.21, %rdx
	callq	_Z21getCmdLineArgumentIntiPPKcS0_
	movl	%eax, -40(%rbp)
.LBB9_5:
	movl	-8(%rbp), %edi
	movq	-16(%rbp), %rsi
	movabsq	$.L.str.22, %rdx
	callq	_Z16checkCmdLineFlagiPPKcS0_
	testb	$1, %al
	jne	.LBB9_6
	jmp	.LBB9_7
.LBB9_6:
	movl	-8(%rbp), %edi
	movq	-16(%rbp), %rsi
	movabsq	$.L.str.22, %rdx
	callq	_Z21getCmdLineArgumentIntiPPKcS0_
	movl	%eax, -36(%rbp)
.LBB9_7:
	movl	-8(%rbp), %edi
	movq	-16(%rbp), %rsi
	movabsq	$.L.str.23, %rdx
	callq	_Z16checkCmdLineFlagiPPKcS0_
	testb	$1, %al
	jne	.LBB9_8
	jmp	.LBB9_9
.LBB9_8:
	movl	-8(%rbp), %edi
	movq	-16(%rbp), %rsi
	movabsq	$.L.str.23, %rdx
	callq	_Z21getCmdLineArgumentIntiPPKcS0_
	movl	%eax, -56(%rbp)
.LBB9_9:
	movl	-8(%rbp), %edi
	movq	-16(%rbp), %rsi
	movabsq	$.L.str.24, %rdx
	callq	_Z16checkCmdLineFlagiPPKcS0_
	testb	$1, %al
	jne	.LBB9_10
	jmp	.LBB9_11
.LBB9_10:
	movl	-8(%rbp), %edi
	movq	-16(%rbp), %rsi
	movabsq	$.L.str.24, %rdx
	callq	_Z21getCmdLineArgumentIntiPPKcS0_
	movl	%eax, -52(%rbp)
.LBB9_11:
	movl	-40(%rbp), %eax
	cmpl	-52(%rbp), %eax
	je	.LBB9_13
# %bb.12:
	movl	-40(%rbp), %esi
	movl	-52(%rbp), %edx
	movabsq	$.L.str.25, %rdi
	movb	$0, %al
	callq	printf
	movl	$1, %edi
	callq	exit
.LBB9_13:
	movl	-40(%rbp), %esi
	movl	-36(%rbp), %edx
	movl	-56(%rbp), %ecx
	movl	-52(%rbp), %r8d
	movabsq	$.L.str.26, %rdi
	movb	$0, %al
	callq	printf
# %bb.14:
	callq	cudaProfilerStart
	movl	%eax, -60(%rbp)
	cmpl	$0, -60(%rbp)
	je	.LBB9_16
# %bb.15:
	movq	stderr, %rax
	movq	%rax, -80(%rbp)                 # 8-byte Spill
	movl	-60(%rbp), %edi
	callq	cudaGetErrorString
	movq	-80(%rbp), %rdi                 # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$397, %ecx                      # imm = 0x18D
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB9_16:
	jmp	.LBB9_17
.LBB9_17:
	movl	-8(%rbp), %edi
	movq	-16(%rbp), %rsi
	movl	-24(%rbp), %edx
	leaq	-40(%rbp), %rcx
	leaq	-56(%rbp), %r8
	callq	_Z14MatrixMultiplyiPPciRK4dim3S3_
	movl	%eax, -64(%rbp)
# %bb.18:
	callq	cudaProfilerStop
	movl	%eax, -68(%rbp)
	cmpl	$0, -68(%rbp)
	je	.LBB9_20
# %bb.19:
	movq	stderr, %rax
	movq	%rax, -88(%rbp)                 # 8-byte Spill
	movl	-68(%rbp), %edi
	callq	cudaGetErrorString
	movq	-88(%rbp), %rdi                 # 8-byte Reload
	movq	%rax, %r8
	movabsq	$.L.str, %rsi
	movabsq	$.L.str.1, %rdx
	movl	$399, %ecx                      # imm = 0x18F
	movb	$0, %al
	callq	fprintf
	movl	$1, %edi
	callq	exit
.LBB9_20:
	jmp	.LBB9_21
.LBB9_21:
	movl	-64(%rbp), %edi
	callq	exit
.Lfunc_end9:
	.size	main, .Lfunc_end9-main
	.cfi_endproc
                                        # -- End function
	.p2align	4, 0x90                         # -- Begin function _ZL14cudaMallocHostPPvmj
	.type	_ZL14cudaMallocHostPPvmj,@function
_ZL14cudaMallocHostPPvmj:               # @_ZL14cudaMallocHostPPvmj
	.cfi_startproc
# %bb.0:
	pushq	%rbp
	.cfi_def_cfa_offset 16
	.cfi_offset %rbp, -16
	movq	%rsp, %rbp
	.cfi_def_cfa_register %rbp
	subq	$32, %rsp
	movq	%rdi, -8(%rbp)
	movq	%rsi, -16(%rbp)
	movl	%edx, -20(%rbp)
	movq	-8(%rbp), %rdi
	movq	-16(%rbp), %rsi
	movl	-20(%rbp), %edx
	callq	cudaHostAlloc
	addq	$32, %rsp
	popq	%rbp
	.cfi_def_cfa %rsp, 8
	retq
.Lfunc_end10:
	.size	_ZL14cudaMallocHostPPvmj, .Lfunc_end10-_ZL14cudaMallocHostPPvmj
	.cfi_endproc
                                        # -- End function
	.p2align	4, 0x90                         # -- Begin function __cuda_register_globals
	.type	__cuda_register_globals,@function
__cuda_register_globals:                # @__cuda_register_globals
	.cfi_startproc
# %bb.0:
	subq	$40, %rsp
	.cfi_def_cfa_offset 48
	movq	%rdi, 32(%rsp)                  # 8-byte Spill
	movabsq	$_Z28__device_stub__MatrixMulCUDAILi16EEvPfS0_S0_ii, %rsi
	movabsq	$.L__unnamed_1, %rcx
	movl	$4294967295, %r8d               # imm = 0xFFFFFFFF
	xorl	%eax, %eax
	movl	%eax, %r9d
	movq	%rcx, %rdx
	movq	$0, (%rsp)
	movq	$0, 8(%rsp)
	movq	$0, 16(%rsp)
	movq	$0, 24(%rsp)
	callq	__cudaRegisterFunction
	movq	32(%rsp), %rdi                  # 8-byte Reload
	movabsq	$_Z28__device_stub__MatrixMulCUDAILi32EEvPfS0_S0_ii, %rsi
	movabsq	$.L__unnamed_2, %rcx
	movl	$4294967295, %r8d               # imm = 0xFFFFFFFF
	xorl	%eax, %eax
	movl	%eax, %r9d
	movq	%rcx, %rdx
	movq	$0, (%rsp)
	movq	$0, 8(%rsp)
	movq	$0, 16(%rsp)
	movq	$0, 24(%rsp)
	callq	__cudaRegisterFunction
	addq	$40, %rsp
	.cfi_def_cfa_offset 8
	retq
.Lfunc_end11:
	.size	__cuda_register_globals, .Lfunc_end11-__cuda_register_globals
	.cfi_endproc
                                        # -- End function
	.p2align	4, 0x90                         # -- Begin function __cuda_module_ctor
	.type	__cuda_module_ctor,@function
__cuda_module_ctor:                     # @__cuda_module_ctor
	.cfi_startproc
# %bb.0:
	pushq	%rax
	.cfi_def_cfa_offset 16
	movabsq	$__cuda_fatbin_wrapper, %rdi
	callq	__cudaRegisterFatBinary
	movq	%rax, %rdi
	movq	%rdi, (%rsp)                    # 8-byte Spill
	movq	%rdi, __cuda_gpubin_handle
	callq	__cuda_register_globals
	movq	(%rsp), %rdi                    # 8-byte Reload
	callq	__cudaRegisterFatBinaryEnd
	movabsq	$__cuda_module_dtor, %rdi
	callq	atexit
	popq	%rax
	.cfi_def_cfa_offset 8
	retq
.Lfunc_end12:
	.size	__cuda_module_ctor, .Lfunc_end12-__cuda_module_ctor
	.cfi_endproc
                                        # -- End function
	.p2align	4, 0x90                         # -- Begin function __cuda_module_dtor
	.type	__cuda_module_dtor,@function
__cuda_module_dtor:                     # @__cuda_module_dtor
	.cfi_startproc
# %bb.0:
	pushq	%rax
	.cfi_def_cfa_offset 16
	movq	__cuda_gpubin_handle, %rdi
	callq	__cudaUnregisterFatBinary
	popq	%rax
	.cfi_def_cfa_offset 8
	retq
.Lfunc_end13:
	.size	__cuda_module_dtor, .Lfunc_end13-__cuda_module_dtor
	.cfi_endproc
                                        # -- End function
	.type	.L.str,@object                  # @.str
	.section	.rodata.str1.1,"aMS",@progbits,1
.L.str:
	.asciz	"CUDA error at %s:%d - %s\n"
	.size	.L.str, 26

	.type	.L.str.1,@object                # @.str.1
.L.str.1:
	.asciz	"matrixMul.cu"
	.size	.L.str.1, 13

	.type	.L.str.2,@object                # @.str.2
.L.str.2:
	.asciz	"No CUDA capable devices found\n"
	.size	.L.str.2, 31

	.type	.L.str.3,@object                # @.str.3
.L.str.3:
	.asciz	"Using device %d: %s\n"
	.size	.L.str.3, 21

	.type	.L.str.4,@object                # @.str.4
.L.str.4:
	.asciz	"Failed to allocate host matrix C!\n"
	.size	.L.str.4, 35

	.type	.L.str.5,@object                # @.str.5
.L.str.5:
	.asciz	"Computing result using CUDA Kernel...\n"
	.size	.L.str.5, 39

	.type	.L.str.6,@object                # @.str.6
.L.str.6:
	.asciz	"done\n"
	.size	.L.str.6, 6

	.type	.L.str.7,@object                # @.str.7
.L.str.7:
	.asciz	"Performance= %.2f GFlop/s, Time= %.3f msec, Size= %.0f Ops, WorkgroupSize= %u threads/block\n"
	.size	.L.str.7, 93

	.type	.L.str.8,@object                # @.str.8
.L.str.8:
	.asciz	"Checking computed result for correctness: "
	.size	.L.str.8, 43

	.type	.L.str.9,@object                # @.str.9
.L.str.9:
	.asciz	"Error! Matrix[%05d]=%.8f, ref=%.8f error term is > %E\n"
	.size	.L.str.9, 55

	.type	.L.str.10,@object               # @.str.10
.L.str.10:
	.asciz	"%s\n"
	.size	.L.str.10, 4

	.type	.L.str.11,@object               # @.str.11
.L.str.11:
	.asciz	"Result = PASS"
	.size	.L.str.11, 14

	.type	.L.str.12,@object               # @.str.12
.L.str.12:
	.asciz	"Result = FAIL"
	.size	.L.str.12, 14

	.type	.L.str.13,@object               # @.str.13
.L.str.13:
	.asciz	"\nNOTE: The CUDA Samples are not meant for performance measurements. Results may vary when GPU Boost is enabled.\n"
	.size	.L.str.13, 113

	.type	.L.str.14,@object               # @.str.14
.L.str.14:
	.asciz	"[Matrix Multiply Using CUDA] - Starting...\n"
	.size	.L.str.14, 44

	.type	.L.str.15,@object               # @.str.15
.L.str.15:
	.asciz	"help"
	.size	.L.str.15, 5

	.type	.L.str.16,@object               # @.str.16
.L.str.16:
	.asciz	"?"
	.size	.L.str.16, 2

	.type	.L.str.17,@object               # @.str.17
.L.str.17:
	.asciz	"Usage -device=n (n >= 0 for deviceID)\n"
	.size	.L.str.17, 39

	.type	.L.str.18,@object               # @.str.18
.L.str.18:
	.asciz	"      -wA=WidthA -hA=HeightA (Width x Height of Matrix A)\n"
	.size	.L.str.18, 59

	.type	.L.str.19,@object               # @.str.19
.L.str.19:
	.asciz	"      -wB=WidthB -hB=HeightB (Width x Height of Matrix B)\n"
	.size	.L.str.19, 59

	.type	.L.str.20,@object               # @.str.20
.L.str.20:
	.asciz	"  Note: Outer matrix dimensions of A & B matrices must be equal.\n"
	.size	.L.str.20, 66

	.type	.L.str.21,@object               # @.str.21
.L.str.21:
	.asciz	"wA"
	.size	.L.str.21, 3

	.type	.L.str.22,@object               # @.str.22
.L.str.22:
	.asciz	"hA"
	.size	.L.str.22, 3

	.type	.L.str.23,@object               # @.str.23
.L.str.23:
	.asciz	"wB"
	.size	.L.str.23, 3

	.type	.L.str.24,@object               # @.str.24
.L.str.24:
	.asciz	"hB"
	.size	.L.str.24, 3

	.type	.L.str.25,@object               # @.str.25
.L.str.25:
	.asciz	"Error: outer matrix dimensions must be equal. (%d != %d)\n"
	.size	.L.str.25, 58

	.type	.L.str.26,@object               # @.str.26
.L.str.26:
	.asciz	"MatrixA(%d,%d), MatrixB(%d,%d)\n"
	.size	.L.str.26, 32

	.type	.L__unnamed_1,@object           # @0
.L__unnamed_1:
	.asciz	"_Z13MatrixMulCUDAILi16EEvPfS0_S0_ii"
	.size	.L__unnamed_1, 36

	.type	.L__unnamed_2,@object           # @1
.L__unnamed_2:
	.asciz	"_Z13MatrixMulCUDAILi32EEvPfS0_S0_ii"
	.size	.L__unnamed_2, 36

	.type	.L__unnamed_3,@object           # @2
	.section	.nv_fatbin,"a",@progbits
	.p2align	3
.L__unnamed_3:
	.asciz	"P\355U\272\001\000\020\000\320b\000\000\000\000\000\000\002\000\001\001@\000\000\000\300Y\000\000\000\000\000\000\000\000\000\000\000\000\000\000\007\000\001\0002\000\000\000\000\000\000\000\000\000\000\000\021\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\177ELF\002\001\0013\007\000\000\000\000\000\000\000\002\000\276\000u\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000V\000\000\000\000\000\0002\0052\000@\000\000\000\000\000@\000\017\000\001\000\000.shstrtab\000.strtab\000.symtab\000.symtab_shndx\000.nv.info\000.text._Z13MatrixMulCUDAILi32EEvPfS0_S0_ii\000.nv.info._Z13MatrixMulCUDAILi32EEvPfS0_S0_ii\000.nv.shared._Z13MatrixMulCUDAILi32EEvPfS0_S0_ii\000.nv.global\000.nv.constant0._Z13MatrixMulCUDAILi32EEvPfS0_S0_ii\000.text._Z13MatrixMulCUDAILi16EEvPfS0_S0_ii\000.nv.info._Z13MatrixMulCUDAILi16EEvPfS0_S0_ii\000.nv.shared._Z13MatrixMulCUDAILi16EEvPfS0_S0_ii\000.nv.constant0._Z13MatrixMulCUDAILi16EEvPfS0_S0_ii\000.nv.rel.action\000\000.shstrtab\000.strtab\000.symtab\000.symtab_shndx\000.nv.info\000_Z13MatrixMulCUDAILi32EEvPfS0_S0_ii\000.text._Z13MatrixMulCUDAILi32EEvPfS0_S0_ii\000.nv.info._Z13MatrixMulCUDAILi32EEvPfS0_S0_ii\000.nv.shared._Z13MatrixMulCUDAILi32EEvPfS0_S0_ii\000.nv.global\000blockIdx\000threadIdx\000$_Z13MatrixMulCUDAILi32EEvPfS0_S0_ii$_ZZ13MatrixMulCUDAILi32EEvPfS0_S0_iiE2As\000$_Z13MatrixMulCUDAILi32EEvPfS0_S0_ii$_ZZ13MatrixMulCUDAILi32EEvPfS0_S0_iiE2Bs\000.nv.constant0._Z13MatrixMulCUDAILi32EEvPfS0_S0_ii\000_param\000_Z13MatrixMulCUDAILi16EEvPfS0_S0_ii\000.text._Z13MatrixMulCUDAILi16EEvPfS0_S0_ii\000.nv.info._Z13MatrixMulCUDAILi16EEvPfS0_S0_ii\000.nv.shared._Z13MatrixMulCUDAILi16EEvPfS0_S0_ii\000$_Z13MatrixMulCUDAILi16EEvPfS0_S0_ii$_ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2As\000$_Z13MatrixMulCUDAILi16EEvPfS0_S0_ii$_ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2Bs\000.nv.constant0._Z13MatrixMulCUDAILi16EEvPfS0_S0_ii\000.nv.rel.action\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000V\000\000\000\003\000\n\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\255\000\000\000\003\000\f\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\334\000\000\000\003\000\r\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\347\000\000\000\001\000\r\000\000\000\000\000\000\000\000\000\001\000\000\000\000\000\000\000\360\000\000\000\001\000\r\000\001\000\000\000\000\000\000\000\001\000\000\000\000\000\000\000\226\001\000\000\003\000\b\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\363\001\000\000\003\000\013\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000J\002\000\000\003\000\016\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\025\003\000\000\003\000\t\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000G\003\000\000\003\000\007\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\0002\000\000\000\022\020\n\000\000\000\000\000\000\000\000\000\000%\000\000\000\000\000\000\317\001\000\000\022\020\013\000\000\000\000\000\000\000\000\000\000%\000\000\000\000\000\000\004/\b\000\f\000\000\000\016\000\000\000\004#\b\000\f\000\000\000\000\000\000\000\004\022\b\000\f\000\000\000X\000\000\000\004\021\b\000\f\000\000\000X\000\000\000\004/\b\000\013\000\000\000\016\000\000\000\004#\b\000\013\000\000\000\000\000\000\000\004\022\b\000\013\000\000\000X\000\000\000\004\021\b\000\013\000\000\000X\000\000\000\0047\004\000u\000\000\000\0010\000\000\001*\000\000\004\n\b\000\006\000\000\000@\001 \000\003\031 \000\004\027\f\000\000\000\000\000\004\000\034\000\000\360\021\000\004\027\f\000\000\000\000\000\003\000\030\000\000\360\021\000\004\027\f\000\000\000\000\000\002\000\020\000\000\360!\000\004\027\f\000\000\000\000\000\001\000\b\000\000\360!\000\004\027\f\000\000\000\000\000\000\000\000\000\000\360!\000\003\033\377\000\004\035\b\000h\004\000\000\330\004\000\000\004\034\004\000\320$\000\000\0044 \000\210\r\000\000\000\000\000\000\001\000\000\000\340\037\000\000\330\027\000\000\000\000\000\000\001\000\000\000`\035\000\000\004\036\004\000 \002\000\000\0047\004\000u\000\000\000\0010\000\000\001*\000\000\004\n\b\000\t\000\000\000@\001 \000\003\031 \000\004\027\f\000\000\000\000\000\004\000\034\000\000\360\021\000\004\027\f\000\000\000\000\000\003\000\030\000\000\360\021\000\004\027\f\000\000\000\000\000\002\000\020\000\000\360!\000\004\027\f\000\000\000\000\000\001\000\b\000\000\360!\000\004\027\f\000\000\000\000\000\000\000\000\000\000\360!\000\003\033\377\000\004\035\b\000h\004\000\000\330\004\000\000\004\034\004\000\320$\000\000\0044 \000\210\r\000\000\000\000\000\000\001\000\000\000\340\037\000\000\330\027\000\000\000\000\000\000\001\000\000\000`\035\000\000\004\036\004\000 \002\000\000K\000\000\000\000\000\000\000\000\002\002\b\020\n/\"\000\000\000\b\000\000\000\000\000\000\b\b\000\000\000\000\000\000\020\b\000\000\000\000\000\000\030\b\000\000\000\000\000\000 \b\000\000\000\000\000\000(\b\000\000\000\000\000\0000\b\000\000\000\000\000\0008\b\000\000\000\000\001\000\000\b\000\000\000\000\001\000\b\b\000\000\000\000\001\000\020\b\000\000\000\000\001\000\030\b\000\000\000\000\001\000 \b\000\000\000\000\001\000(\b\000\000\000\000\001\0000\b\000\000\000\000\001\0008\b\000\000\000\000\002\000\000\b\000\000\000\000\002\000\b\b\000\000\000\000\002\000\020\b\000\000\000\000\002\000\030\b\000\000\000\000\002\000 \b\000\000\000\000\002\000(\b\000\000\000\000\002\0000\b\000\000\000\000\002\0008\b\000\000\000\000\000\000\000\024,\000\000\000\t\000\000\f\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\357\037\340\375\003<d\000\001\000\207\000\200\007\230L\001\001\207\372\377\377\017\034\000\000w\003\000\000\310\360\357\037\340\375\003\274\177\000\007\001\007\000\200\003l[\017\000\200\000\000\000@\342\300\000\020\000\000\000\240\343\357\037\340!\003\274\177\000\000\001\367\017\000\000\020\\\000\n\007\000\000\000\340\\\002\000\007\000\200\007\230\\\357\037\340\375\003\274\177\000\003\000\367\017\200\007\230\\\000\000'\000\200\007\230\\\004\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\003\000\007\000\200\007\230\\\004\000G\000\200\007\230\\\000\000\027\000\200\007\230L\357\037\340\375\003\274\177\000\002\000\367\017\200\007\230\\\000\003\007\000\000\002G\\\002\004'\000\000\002G\\\357\037\340!\003\274\177\000\003\360\307\025\000\000\000\001\003\003\007\000\000\000\224\357\003\0007\000\200\007\230\\\357\037\340!\003\274\177\000\004\360\207\025\000\000\000\001\004\004\007\000\000\000\224\357\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\006\360\007\025\000\000\000\001\006\006\007\000\000\000\225\357\005\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\f\000w\000\200\007\230\\\005\000W\000\200\007\230\\\f\000\307\000\200\007\230\\\357\037\340!\003\274\177\000\006\360\207\024\000\000\000\001\006\006\007\000\000\000\225\357\b\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\t\000w\000\200\007\230\\\b\000\207\000\200\007\230\\\t\000\227\000\200\007\230\\\357\037\340!\003\274\177\000\006\360\007\024\000\000\000\001\006\006\007\000\000\000\225\357\n\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\013\000w\000\200\007\230\\\n\000\247\000\200\007\230\\\013\000\267\000\200\007\230\\\357\037\340\375\003\274\177\000\006\000W\000\200\007\230\\\007\000\307\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\007\000w\000\200\007\230\\\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\b\000\207\000\200\007\230\\\t\000\227\000\200\007\230\\\b\000\207\000\200\007\230\\\357\037\340\375\003\274\177\000\t\000\227\000\200\007\230\\\b\000\207\000\200\007\230\\\t\000\227\000\200\007\230\\\357\037\340\375\003\274\177\000\n\000\247\000\200\007\230\\\013\000\267\000\200\007\230\\\n\000\247\000\200\007\230\\\357\037\340\375\003\274\177\000\013\000\267\000\200\007\230\\\n\000\247\000\200\007\230\\\013\000\267\000\200\007\230\\\357\037\340\375\003\274\177\000\r\000\367\017\000\200\020\\\005\002\367\017\000\b\020\\\r\000\327\000\200\007\230\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\f\r\367\017\000\200\327[\r\r\367\017\300\002\330[\357\037\340\375\003\274g\000\f\000\307\000\200\007\230\\\r\000\327\000\200\007\230\\\n\f\007\000\000\000\260\240\357\037\340\375\003\274\177\000\013\000\207\000\000\000\020\034\005\002\367\017\000\b\020\\\013\000\267\000\200\007\230\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\n\013\367\017\000\200\327[\013\013\367\017\300\002\330[\357\037\340\375\003\274g\000\n\000\247\000\200\007\230\\\013\000\267\000\200\007\230\\\b\n\007\000\000\000\260\240\357\037\340\375\003\274\177\000\t\000\007\001\000\000\020\034\005\002\367\017\000\b\020\\\t\000\227\000\200\007\230\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\b\t\367\017\000\200\327[\t\t\367\017\300\002\330[\357\037\340\375\003\274g\000\b\000\207\000\200\007\230\\\t\000\227\000\200\007\230\\\006\b\007\000\000\000\260\240\357\037\340\375\003\274\177\000\007\000\207\001\000\000\020\034\005\002\367\017\000\b\020\\\007\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\006\007\367\017\000\200\327[\007\007\367\017\300\002\330[\357\037\340\375\003\274g\000\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\004\006\007\000\000\000\220\240\357\037\340\375\003\274\177\000\005\000\307\001\000\000\020\034\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\357\037\340\375\003\274g\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\017\031\340\375\003\274\177\000\003\000W\002\000\000\310\360\003\0007\000\200\007\230\\\005\000\007\002\000\000\020\034\357\037\340\375\003\274\177\000\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340=\003<d\000\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\003\000g\002\000\000\310\360\357\037\340\375\003\274\177\000\003\0007\000\200\007\230\\\005\000G\002\000\000\020\034\006\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\031\340!\003\274\177\000\003\004\007\000\000\000\220\240\003\000\027\002\000\000\310\360\003\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\005\000\207\002\000\000\020\034\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\357\037\340\375\003\274g\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\017\031\340\375\003\274\177\000\003\000'\002\000\000\310\360\003\0007\000\200\007\230\\\005\000\307\002\000\000\020\034\357\037\340\375\003\274\177\000\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340=\003\274\177\000\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\005\000\207\001\000\000\020\034\357\037\340\375\003\274\177\000\003\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\200\005\000G\002\000\000\020\034\357\037\340\375\003\274\177\000\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340!\003<d\000\005\000W\000\200\007\230\\\004\004\007\000\000\000\220\200\003\003G\000\000\0038\\\357\037\340\375\003\274\177\000\003\003W\000\000\000H8\005\000\007\003\000\000\020\034\006\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\031\340\375\003\274\177\000\003\004\007\000\000\000\220\240\005\000\007\003\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340\375\003\274\177\000\003\004\007\000\000\000\220\200\005\000\207\001\000\000\020\034\006\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340\375\003\274\177\000\004\004\007\000\000\000\220\200\003\003G\000\000\000\020\\\003\003\367\377\377\377\017\034\357\037\340\375\003\274\177\000\005\000G\003\000\000\020\034\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\357\037\340\375\003\274g\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\357\037\340\375\003\274\177\000\006\360\007\002\000\000\000\001\005\000\207\003\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\031\340\375\003\274\177\000\006\004\007\000\000\000\220\240\005\000\007\002\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340\375\003\274\177\000\004\004\007\000\000\000\220\200\003\004W\000\000\000H8\005\000\307\003\000\000\020\034\357\037\340\375\003\274\177\000\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340=\003\274\177\000\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\005\000\307\001\000\000\020\034\357\037\340\375\003\274\177\000\003\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\005\000W\000\200\007\230\\\004\004\007\000\000\000\220\200\003\004W\000\000\000H8\357\037\340\375\003\274\177\000\005\000\007\004\000\000\020\034\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\357\037\340\375\003\274g\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\357\037\340\375\003\274\177\000\006\000\367\017\200\007\230\\\005\000G\004\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\031\340\375\003\274\177\000\006\004\007\000\000\000\220\240\005\000\007\003\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340\375\003\274\177\000\003\004\007\000\000\000\220\200\005\000\207\004\000\000\020\034\006\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\031\340\375\003\274\177\000\003\004\007\000\000\000\220\240\005\000\307\003\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340\375\003\274\177\000\003\004\007\000\000\000\220\200\005\000\307\004\000\000\020\034\006\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\031\340\375\003\274\177\000\003\004\007\000\000\000\220\240\000\000\007\000\200\007\230\\\002\000'\000\200\007\230\\\357\037\340\375\003\274\177\000\000\000\0003\001\000\220\342\017\000\007\000\000\000@\342\005\000\207\004\000\000\020\034\357\037\340\375\003\274\177\000\003\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\200\005\000G\003\000\000\020\034\357\037\340\375\003\274\177\000\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\005\000W\000\200\007\230\\\004\004\007\000\000\000\220\200\007\003G\000\200\003i[\357\037\340\375\003\274\177\000\017\000\000\000\000\000\370\360\017\000\007\000\000\000@\342\005\000\207\000\000\000\020\034\357\037\340\375\003\274\177\000\003\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\005\000W\000\200\007\230\\\004\004\007\000\000\000\260\200\003\000G\000\200\007\230\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\007\000\207\004\000\000\020\034\004\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\007\000w\000\200\007\230\\\004\000G\000\200\007\230\\\006\007\367\017\000\200\327[\357\037\340\375\003\274\177\000\007\007\367\017@\002\330[\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\017\031\340\375\003\274\177\000\004\006\007\000\000\000\220\200\007\000\207\001\000\000\020\034\b\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\007\000w\000\200\007\230\\\b\000\207\000\200\007\230\\\006\007\367\017\000\200\327[\357\037\340\375\003\274\177\000\007\007\367\017@\004\330[\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\017\031\340\375\003\274\177\000\b\006\007\000\000\000\220\200\007\000\307\002\000\000\020\034\t\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\007\000w\000\200\007\230\\\t\000\227\000\200\007\230\\\006\007\367\017\000\200\327[\357\037\340\375\003\274\177\000\007\007\367\017\300\004\330[\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\017\031\340!\003\274\177\000\006\006\007\000\000\000\220\200\b\bg\000\000\0038\\\007\004\207\000\000\000\020\\\357\037\340\375\003\274\177\000\t\000\207\002\000\000\020\034\004\002\367\017\000\b\020\\\t\000\227\000\200\007\230\\\357\037\340\375\003\274\177\000\004\000G\000\200\007\230\\\b\t\367\017\000\200\327[\t\t\367\017@\002\330[\357\037\340\375\003<d\000\b\000\207\000\200\007\230\\\t\000\227\000\200\007\230\\\004\b\007\000\000\000\220\200\357\037\340!\003\274\177\000\007\007G\000\000\000\020\\\007:w\000\000\000\340\\\b\007\367\001\000\000)8\357\037\340\375\003\274\177\000\t\000\207\000\200\007\230\\\b\000w\000\200\007\230\\\007\000\207\000\200\007\230\\\357\037\340\375\003\274\177\000\b\000\227\000\200\007\230\\\007\000w\000\200\007\230\\\b\000\207\000\200\007\230\\\357\037\340\375\003\274\177\000\b\007'\000@\004\3706\007\007'\000\000\000H8\003\003w\000\000\200\020\\\357\037\340\375\003\274\177\000\005\005\207\000\000\b\020\\\t\0007\000\200\007\230\\\003\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\b\t\367\017\000\200\327[\t\t\367\017\300\001\330[\b\000\207\000\200\007\230\\\357\037\340!\003<d\000\t\000\227\000\200\007\230\\\003\b\007\000\000\000\220\200\006:g\000\000\000\340\\\357\037\340\375\003\274\177\000\007\006\367\001\000\000)8\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\t\000g\000\200\007\230\\\n\000w\000\200\007\230\\\t\000\227\000\200\007\230\\\357\037\340!\003\274\177\000\n\000\247\000\200\007\230\\\006\n\367\017\000\000\340\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\007\000\367\017\200\007\230\\\005\000g\000\200\007\230\\\b\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\007\000W\000\200\007\230\\\b\000\207\000\200\007\230\\\005\000\007\000\200\007\230L\357\037\340\375\003\274\177\000\006\000\367\017\200\007\230\\\005\007W\000\000\002G\\\006\bg\000\000\002G\\\357\037\340\375\003\274\177\000\b\tw\000@\005\3706\007\tw\000\000\000H8\007\005w\000\000\200\020\\\357\037\340!\003\274\177\000\b\006\207\000\000\b\020\\\004:G\000\000\000\340\\\005\004\367\001\000\000)8\357\037\340\375\003\274\177\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\006\000G\000\200\007\230\\\357\037\340\375\003\274\177\000\004\000W\000\200\007\230\\\005\000g\000\200\007\230\\\006\000G\000\200\007\230\\\357\037\340\375\003\274\177\000\006\005'\000@\003\3706\005\005'\000\000\000H8\005\007W\000\000\200\020\\\357\037\340\375\003\274\177\000\006\bg\000\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340=\003\274\177\000\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\005\000\007\001\000\000\020\034\357\037\340\375\003\274\177\000\003\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\005\000W\000\200\007\230\\\004\004\007\000\000\000\260\200\003\000G\000\200\007\230\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\007\000\307\004\000\000\020\034\004\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\007\000w\000\200\007\230\\\004\000G\000\200\007\230\\\006\007\367\017\000\200\327[\357\037\340\375\003\274\177\000\007\007\367\017@\002\330[\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\017\031\340\375\003\274\177\000\004\006\007\000\000\000\220\200\007\000\307\001\000\000\020\034\b\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\007\000w\000\200\007\230\\\b\000\207\000\200\007\230\\\006\007\367\017\000\200\327[\357\037\340\375\003\274\177\000\007\007\367\017@\004\330[\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\017\031\340\375\003\274\177\000\b\006\007\000\000\000\220\200\007\000\307\002\000\000\020\034\t\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\007\000w\000\200\007\230\\\t\000\227\000\200\007\230\\\006\007\367\017\000\200\327[\357\037\340\375\003\274\177\000\007\007\367\017\300\004\330[\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\017\031\340!\003\274\177\000\006\006\007\000\000\000\220\200\b\bg\000\000\0038\\\007\004\207\000\000\000\020\\\357\037\340\375\003\274\177\000\t\000\207\002\000\000\020\034\004\002\367\017\000\b\020\\\t\000\227\000\200\007\230\\\357\037\340\375\003\274\177\000\004\000G\000\200\007\230\\\b\t\367\017\000\200\327[\t\t\367\017@\002\330[\357\037\340\375\003<d\000\b\000\207\000\200\007\230\\\t\000\227\000\200\007\230\\\004\b\007\000\000\000\220\200\357\037\340!\003\274\177\000\007\007G\000\000\000\020\\\007:w\000\000\000\340\\\b\007\367\001\000\000)8\357\037\340\375\003\274\177\000\t\000\207\000\200\007\230\\\b\000w\000\200\007\230\\\007\000\207\000\200\007\230\\\357\037\340\375\003\274\177\000\b\000\227\000\200\007\230\\\007\000w\000\200\007\230\\\b\000\207\000\200\007\230\\\357\037\340\375\003\274\177\000\b\007'\000@\004\3706\007\007'\000\000\000H8\003\003w\000\000\200\020\\\357\037\340\375\003\274\177\000\005\005\207\000\000\b\020\\\t\0007\000\200\007\230\\\003\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\b\t\367\017\000\200\327[\t\t\367\017\300\001\330[\b\000\207\000\200\007\230\\\357\037\340!\003<d\000\t\000\227\000\200\007\230\\\003\b\007\000\000\000\220\200\006:g\000\000\000\340\\\357\037\340\375\003\274\177\000\007\006\367\001\000\000)8\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\t\000g\000\200\007\230\\\n\000w\000\200\007\230\\\t\000\227\000\200\007\230\\\357\037\340!\003\274\177\000\n\000\247\000\200\007\230\\\006\n\007\000\001\000\3408\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\007\000\367\017\200\007\230\\\005\000g\000\200\007\230\\\b\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\007\000W\000\200\007\230\\\b\000\207\000\200\007\230\\\005\000\007\000\200\007\230L\357\037\340\375\003\274\177\000\006\000\367\017\200\007\230\\\005\007W\000\000\002G\\\006\bg\000\000\002G\\\357\037\340\375\003\274\177\000\b\tw\000@\005\3706\007\tw\000\000\000H8\007\005w\000\000\200\020\\\357\037\340!\003\274\177\000\b\006\207\000\000\b\020\\\004:G\000\000\000\340\\\005\004\367\001\000\000)8\357\037\340\375\003\274\177\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\006\000G\000\200\007\230\\\357\037\340\375\003\274\177\000\004\000W\000\200\007\230\\\005\000g\000\200\007\230\\\006\000G\000\200\007\230\\\357\037\340\375\003\274\177\000\006\005'\000@\003\3706\005\005'\000\000\000H8\005\007W\000\000\200\020\\\357\037\340\375\003\274\177\000\006\bg\000\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340=\003\274g\000\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\000\000\007\000\200\033\250\360\365\007\340\375\003\274\177\000\000\000\007\000\000\000\230\357\006\000\367\017\200\007\230\\\005\000\007\005\000\000\020\034\357\037\340\375\003\274\177\000\003\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\357\037\340=\003\274\177\000\005\000W\000\200\007\230\\\006\004\007\000\000\000\220\240\000\000\000`\000\000\220\342\357\037\340\375\003\274\177\000\017\000\007\000\000\000@\342\005\000\007\005\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340\375\003\274\177\000\004\004\007\000\000\000\220\200\007\004\367\001\200\003i6\017\000\000\000\000\000\370\360\357\037\340\375\003\274\177\000\017\000\007\000\000\000@\342\005\000\307\002\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340!\003\274\177\000\004\004\007\000\000\000\220\200\004:G\000\000\000\340\\\003\004\367\001\000\000)8\357\037\340\375\003\274\177\000\004\000G\000\200\007\230\\\003\0007\000\200\007\230\\\t\004w\000\300\001\3706\357\037\340!\003\274\177\000\006\004w\000\000\000H8\004\n\367\017\000\000\340\\\004\000G\000\200\007\230\\\357\037\340\375\003\274\177\000\005\000\367\017\200\007\230\\\003\000G\000\200\007\230\\\004\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\003\0007\000\200\007\230\\\004\000G\000\200\007\230\\\007\000\007\000\200\007\230L\357\037\340\375\003\274\177\000\b\000\367\017\200\007\230\\\007\003w\000\000\002G\\\b\004\207\000\000\002G\\\357\037\340\375\003\274\177\000\007\007g\000\000\200\020\\\b\b\227\000\000\b\020\\\005\000\007\005\000\000\020\034\357\037\340\375\003\274\177\000\003\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\357\037\340!\003<d\000\005\000W\000\200\007\230\\\004\004\007\000\000\000\220\200\004:G\000\000\000\340\\\357\037\340\375\003\274\177\000\003\004\367\001\000\000)8\006\000G\000\200\007\230\\\t\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\003\006'\000\300\004\3706\005\006'\000\000\000H8\005\007W\000\000\200\020\\\357\037\340\375\003\274\177\000\003\b7\000\000\b\020\\\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\200\t\006w\000\300\004\3706\357\037\340!\003\274\177\000\006\006w\000\000\000H8\004\n\007\000\001\000\3408\004\000G\000\200\007\230\\\357\037\340\375\003\274\177\000\005\000\367\017\200\007\230\\\007\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\004\000w\000\200\007\230\\\005\000W\000\200\007\230\\\007\000\007\000\200\007\230L\357\037\340\375\003\274\177\000\b\000\367\017\200\007\230\\\007\004w\000\000\002G\\\b\005\207\000\000\002G\\\357\037\340\375\003\274\177\000\007\007g\000\000\200\020\\\b\b\227\000\000\b\020\\\005\000\207\002\000\000\020\034\357\037\340\375\003\274\177\000\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340!\003<d\000\005\000W\000\200\007\230\\\004\004\007\000\000\000\220\200\004:G\000\000\000\340\\\357\037\340\375\003\274\177\000\005\004\367\001\000\000)8\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\006\004'\000\300\002\3706\005\004'\000\000\000H8\005\007W\000\000\200\020\\\357\037\340\375\003\274\177\000\006\bg\000\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\005\000W\000\200\007\230\\\006\004\007\000\000\000\220\200\005\000G\004\000\000\020\034\357\037\340\375\003\274\177\000\007\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\007\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017\300\003\330[\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\005\000W\000\200\007\230\\\004\004\007\000\000\000\220\200\003\003g\000\000\002\200Y\357\037\340\375\003\274\177\000\005\000G\004\000\000\020\034\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\357\037\340\375\003\274g\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\357\037\340\375\003\274\177\000\017\000\007\000\000\000@\342\005\000\007\005\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340\375\003\274\177\000\004\004\007\000\000\000\220\200\003\004\027\000\000\000\000\034\005\000\007\005\000\000\020\034\357\037\340\375\003\274\177\000\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340=\003\274\177\000\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\017\000\007\241\377\017@\342\357\031\240\376\000\274\177\000\000\000\007\000\200\033\250\360\000\000\007\000\000\000\230\357\017\000\007\000\000\000@\342\357\037\340\375\003\274\177\000\005\000\207\003\000\000\020\034\003\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\005\005\367\017\300\001\330[\357\037\340\375\003<d\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\200\357\037\340\375\003\274\177\000\005\000\207\004\000\000\020\034\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\357\037\340\375\003<d\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\004\004\007\000\000\000\220\200\357\037\340\375\003\274\177\000\003\0047\000\000\000\020\\\005\000\207\004\000\000\020\034\006\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\031\340\375\003\274\177\000\003\004\007\000\000\000\220\240\005\000\007\004\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340\375\003\274\177\000\003\004\007\000\000\000\220\200\005\000\307\004\000\000\020\034\006\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340\375\003\274\177\000\004\004\007\000\000\000\220\200\003\0047\000\000\000\020\\\005\000\307\004\000\000\020\034\357\037\340\375\003\274\177\000\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340=\003\274\177\000\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\017\000\207\315\376\017@\342\357\037\340\375\003\274\177\000\005\000\307\001\000\000\020\034\003\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\005\005\367\017\300\001\330[\357\037\340\375\003<d\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\200\357\037\340\375\003\274\177\000\005\000G\002\000\000\020\034\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\357\037\340\375\003<d\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\004\004\007\000\000\000\220\200\017\031\340\375\003\274\177\000\003\003G\000\000\0038\\\003\003W\000\000\000H8\005\000\007\002\000\000\020\034\357\037\340\375\003\274\177\000\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\005\000W\000\200\007\230\\\004\004\007\000\000\000\220\200\004\004W\000\000\000H8\357\037\340\375\003\274\177\000\003\003G\000\000\000\020\\\005\000G\005\000\000\020\034\006\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\031\340\375\003\274\177\000\003\004\007\000\000\000\220\240\005\000G\004\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340\375\003\274\177\000\003\004\007\000\000\000\220\200\007\000\367\017\000\200\020\\\004\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\007\000w\000\200\007\230\\\004\000G\000\200\007\230\\\006\007\367\017\000\200\327[\357\037\340\375\003\274\177\000\007\007\367\017@\002\330[\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\017\031\340\375\003\274\177\000\006\006\007\000\000\000\260\200\004\000g\000\200\007\230\\\005\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\007\000G\005\000\000\020\034\b\002\367\017\000\b\020\\\007\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\b\000\207\000\200\007\230\\\006\007\367\017\000\200\327[\007\007\367\017@\004\330[\357\037\340\375\003<d\000\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\b\006\007\000\000\000\220\200\357\037\340\375\003\274\177\000\007\000\307\001\000\000\020\034\t\002\367\017\000\b\020\\\007\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\t\000\227\000\200\007\230\\\006\007\367\017\000\200\327[\007\007\367\017\300\004\330[\357\037\340\375\003<d\000\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\t\006\007\000\000\000\220\200\357\037\340\375\003\274\177\000\007\000\307\002\000\000\020\034\n\002\367\017\000\b\020\\\007\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\n\000\247\000\200\007\230\\\006\007\367\017\000\200\327[\007\007\367\017@\005\330[\357\037\340\375\003<d\000\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\006\006\007\000\000\000\220\200\017\031\340\375\003\274\177\000\006\tg\000\000\0038\\\b\bg\000\000\000\020\\\007\000\207\002\000\000\020\034\357\037\340\375\003\274\177\000\000\002\367\017\000\b\020\\\007\000w\000\200\007\230\\\000\000\007\000\200\007\230\\\357\037\340\375\003\274\177\000\006\007\367\017\000\200\327[\007\007\367\017@\000\330[\006\000g\000\200\007\230\\\357\037\340!\003\274\177\000\007\000w\000\200\007\230\\\006\006\007\000\000\000\220\200\006\bg\000\000\000\020\\\017\031\340\375\003\274\177\000\006:g\000\000\000\340\\\007\006\367\001\000\000)8\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\007\000w\000\200\007\230\\\000\000g\000\200\007\230\\\002\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\000\000\007\000\200\007\230\\\002\000'\000\200\007\230\\\002\000'\000@\001\3706\357\037\340\375\003\274\177\000\000\000'\000\000\000H8\004\004\007\000\000\200\020\\\000\005'\000\000\b\020\\\357\037\340\375\003\274\177\000\005\000G\000\200\007\230\\\000\000\007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017@\000\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\031\340\375\003\374\037\000\003\004\007\000\000\000\220\240\017\000\007\000\000\000\000\343\017\000\207\377\377\017@\342\340\007\000\374\000\200\037\000\000\017\007\000\000\000\260P\000\017\007\000\000\000\260P\000\017\007\000\000\000\260P\357\037\340\375\003<d\000\001\000\207\000\200\007\230L\001\001\207\372\377\377\017\034\000\000w\003\000\000\310\360\357\037\340\375\003\274\177\000\007\001\007\000\200\003l[\017\000\200\000\000\000@\342\300\000\020\000\000\000\240\343\357\037\340!\003\274\177\000\000\001\367\017\000\000\020\\\000\n\007\000\000\000\340\\\002\000\007\000\200\007\230\\\357\037\340\375\003\274\177\000\003\000\367\017\200\007\230\\\000\000'\000\200\007\230\\\004\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\003\000\007\000\200\007\230\\\004\000G\000\200\007\230\\\000\000\027\000\200\007\230L\357\037\340\375\003\274\177\000\002\000\367\017\200\007\230\\\000\003\007\000\000\002G\\\002\004'\000\000\002G\\\357\037\340!\003\274\177\000\003\360\307\025\000\000\000\001\003\003\007\000\000\000\224\357\003\0007\000\200\007\230\\\357\037\340!\003\274\177\000\004\360\207\025\000\000\000\001\004\004\007\000\000\000\224\357\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\006\360\007\025\000\000\000\001\006\006\007\000\000\000\225\357\005\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\f\000w\000\200\007\230\\\005\000W\000\200\007\230\\\f\000\307\000\200\007\230\\\357\037\340!\003\274\177\000\006\360\207\024\000\000\000\001\006\006\007\000\000\000\225\357\b\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\t\000w\000\200\007\230\\\b\000\207\000\200\007\230\\\t\000\227\000\200\007\230\\\357\037\340!\003\274\177\000\006\360\007\024\000\000\000\001\006\006\007\000\000\000\225\357\n\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\013\000w\000\200\007\230\\\n\000\247\000\200\007\230\\\013\000\267\000\200\007\230\\\357\037\340\375\003\274\177\000\006\000W\000\200\007\230\\\007\000\307\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\007\000w\000\200\007\230\\\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\b\000\207\000\200\007\230\\\t\000\227\000\200\007\230\\\b\000\207\000\200\007\230\\\357\037\340\375\003\274\177\000\t\000\227\000\200\007\230\\\b\000\207\000\200\007\230\\\t\000\227\000\200\007\230\\\357\037\340\375\003\274\177\000\n\000\247\000\200\007\230\\\013\000\267\000\200\007\230\\\n\000\247\000\200\007\230\\\357\037\340\375\003\274\177\000\013\000\267\000\200\007\230\\\n\000\247\000\200\007\230\\\013\000\267\000\200\007\230\\\357\037\340\375\003\274\177\000\r\000\367\017\000\200\020\\\005\002\367\017\000\b\020\\\r\000\327\000\200\007\230\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\f\r\367\017\000\200\327[\r\r\367\017\300\002\330[\357\037\340\375\003\274g\000\f\000\307\000\200\007\230\\\r\000\327\000\200\007\230\\\n\f\007\000\000\000\260\240\357\037\340\375\003\274\177\000\013\000\207\000\000\000\020\034\005\002\367\017\000\b\020\\\013\000\267\000\200\007\230\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\n\013\367\017\000\200\327[\013\013\367\017\300\002\330[\357\037\340\375\003\274g\000\n\000\247\000\200\007\230\\\013\000\267\000\200\007\230\\\b\n\007\000\000\000\260\240\357\037\340\375\003\274\177\000\t\000\007\001\000\000\020\034\005\002\367\017\000\b\020\\\t\000\227\000\200\007\230\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\b\t\367\017\000\200\327[\t\t\367\017\300\002\330[\357\037\340\375\003\274g\000\b\000\207\000\200\007\230\\\t\000\227\000\200\007\230\\\006\b\007\000\000\000\260\240\357\037\340\375\003\274\177\000\007\000\207\001\000\000\020\034\005\002\367\017\000\b\020\\\007\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\006\007\367\017\000\200\327[\007\007\367\017\300\002\330[\357\037\340\375\003\274g\000\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\004\006\007\000\000\000\220\240\357\037\340\375\003\274\177\000\005\000\307\001\000\000\020\034\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\357\037\340\375\003\274g\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\017\031\340\375\003\274\177\000\003\000W\002\000\000\310\360\003\0007\000\200\007\230\\\005\000\007\002\000\000\020\034\357\037\340\375\003\274\177\000\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340=\003<d\000\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\003\000g\002\000\000\310\360\357\037\340\375\003\274\177\000\003\0007\000\200\007\230\\\005\000G\002\000\000\020\034\006\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\031\340!\003\274\177\000\003\004\007\000\000\000\220\240\003\000\027\002\000\000\310\360\003\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\005\000\207\002\000\000\020\034\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\357\037\340\375\003\274g\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\017\031\340\375\003\274\177\000\003\000'\002\000\000\310\360\003\0007\000\200\007\230\\\005\000\307\002\000\000\020\034\357\037\340\375\003\274\177\000\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340=\003\274\177\000\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\005\000\207\001\000\000\020\034\357\037\340\375\003\274\177\000\003\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\200\005\000G\002\000\000\020\034\357\037\340\375\003\274\177\000\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340!\003<d\000\005\000W\000\200\007\230\\\004\004\007\000\000\000\220\200\003\003G\000\000\0038\\\357\037\340\375\003\274\177\000\003\003G\000\000\000H8\005\000\007\003\000\000\020\034\006\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\031\340\375\003\274\177\000\003\004\007\000\000\000\220\240\005\000\007\003\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340\375\003\274\177\000\003\004\007\000\000\000\220\200\005\000\207\001\000\000\020\034\006\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340\375\003\274\177\000\004\004\007\000\000\000\220\200\003\003G\000\000\000\020\\\003\003\367\377\377\377\017\034\357\037\340\375\003\274\177\000\005\000G\003\000\000\020\034\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\357\037\340\375\003\274g\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\357\037\340\375\003\274\177\000\006\360\007\001\000\000\000\001\005\000\207\003\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\031\340\375\003\274\177\000\006\004\007\000\000\000\220\240\005\000\007\002\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340\375\003\274\177\000\004\004\007\000\000\000\220\200\003\004G\000\000\000H8\005\000\307\003\000\000\020\034\357\037\340\375\003\274\177\000\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340=\003\274\177\000\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\005\000\307\001\000\000\020\034\357\037\340\375\003\274\177\000\003\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\005\000W\000\200\007\230\\\004\004\007\000\000\000\220\200\003\004G\000\000\000H8\357\037\340\375\003\274\177\000\005\000\007\004\000\000\020\034\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\357\037\340\375\003\274g\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\357\037\340\375\003\274\177\000\006\000\367\017\200\007\230\\\005\000G\004\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\031\340\375\003\274\177\000\006\004\007\000\000\000\220\240\005\000\007\003\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340\375\003\274\177\000\003\004\007\000\000\000\220\200\005\000\207\004\000\000\020\034\006\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\031\340\375\003\274\177\000\003\004\007\000\000\000\220\240\005\000\307\003\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340\375\003\274\177\000\003\004\007\000\000\000\220\200\005\000\307\004\000\000\020\034\006\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\031\340\375\003\274\177\000\003\004\007\000\000\000\220\240\000\000\007\000\200\007\230\\\002\000'\000\200\007\230\\\357\037\340\375\003\274\177\000\000\000\0003\001\000\220\342\017\000\007\000\000\000@\342\005\000\207\004\000\000\020\034\357\037\340\375\003\274\177\000\003\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\200\005\000G\003\000\000\020\034\357\037\340\375\003\274\177\000\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\005\000W\000\200\007\230\\\004\004\007\000\000\000\220\200\007\003G\000\200\003i[\357\037\340\375\003\274\177\000\017\000\000\000\000\000\370\360\017\000\007\000\000\000@\342\005\000\207\000\000\000\020\034\357\037\340\375\003\274\177\000\003\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\005\000W\000\200\007\230\\\004\004\007\000\000\000\260\200\003\000G\000\200\007\230\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\007\000\207\004\000\000\020\034\004\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\007\000w\000\200\007\230\\\004\000G\000\200\007\230\\\006\007\367\017\000\200\327[\357\037\340\375\003\274\177\000\007\007\367\017@\002\330[\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\017\031\340\375\003\274\177\000\004\006\007\000\000\000\220\200\007\000\207\001\000\000\020\034\b\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\007\000w\000\200\007\230\\\b\000\207\000\200\007\230\\\006\007\367\017\000\200\327[\357\037\340\375\003\274\177\000\007\007\367\017@\004\330[\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\017\031\340\375\003\274\177\000\b\006\007\000\000\000\220\200\007\000\307\002\000\000\020\034\t\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\007\000w\000\200\007\230\\\t\000\227\000\200\007\230\\\006\007\367\017\000\200\327[\357\037\340\375\003\274\177\000\007\007\367\017\300\004\330[\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\017\031\340!\003\274\177\000\006\006\007\000\000\000\220\200\b\bg\000\000\0038\\\007\004\207\000\000\000\020\\\357\037\340\375\003\274\177\000\t\000\207\002\000\000\020\034\004\002\367\017\000\b\020\\\t\000\227\000\200\007\230\\\357\037\340\375\003\274\177\000\004\000G\000\200\007\230\\\b\t\367\017\000\200\327[\t\t\367\017@\002\330[\357\037\340\375\003<d\000\b\000\207\000\200\007\230\\\t\000\227\000\200\007\230\\\004\b\007\000\000\000\220\200\357\037\340!\003\274\177\000\007\007G\000\000\000\020\\\007:w\000\000\000\340\\\b\007\367\001\000\000)8\357\037\340\375\003\274\177\000\t\000\207\000\200\007\230\\\b\000w\000\200\007\230\\\007\000\207\000\200\007\230\\\357\037\340\375\003\274\177\000\b\000\227\000\200\007\230\\\007\000w\000\200\007\230\\\b\000\207\000\200\007\230\\\357\037\340\375\003\274\177\000\b\007'\000@\004\3706\007\007'\000\000\000H8\003\003w\000\000\200\020\\\357\037\340\375\003\274\177\000\005\005\207\000\000\b\020\\\t\0007\000\200\007\230\\\003\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\b\t\367\017\000\200\327[\t\t\367\017\300\001\330[\b\000\207\000\200\007\230\\\357\037\340!\003<d\000\t\000\227\000\200\007\230\\\003\b\007\000\000\000\220\200\006:g\000\000\000\340\\\357\037\340\375\003\274\177\000\007\006\367\001\000\000)8\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\t\000g\000\200\007\230\\\n\000w\000\200\007\230\\\t\000\227\000\200\007\230\\\357\037\340!\003\274\177\000\n\000\247\000\200\007\230\\\006\n\367\017\000\000\340\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\007\000\367\017\200\007\230\\\005\000g\000\200\007\230\\\b\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\007\000W\000\200\007\230\\\b\000\207\000\200\007\230\\\005\000\007\000\200\007\230L\357\037\340\375\003\274\177\000\006\000\367\017\200\007\230\\\005\007W\000\000\002G\\\006\bg\000\000\002G\\\357\037\340\375\003\274\177\000\b\tg\000@\005\3706\007\tg\000\000\000H8\007\005w\000\000\200\020\\\357\037\340!\003\274\177\000\b\006\207\000\000\b\020\\\004:G\000\000\000\340\\\005\004\367\001\000\000)8\357\037\340\375\003\274\177\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\006\000G\000\200\007\230\\\357\037\340\375\003\274\177\000\004\000W\000\200\007\230\\\005\000g\000\200\007\230\\\006\000G\000\200\007\230\\\357\037\340\375\003\274\177\000\006\005'\000@\003\3706\005\005'\000\000\000H8\005\007W\000\000\200\020\\\357\037\340\375\003\274\177\000\006\bg\000\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340=\003\274\177\000\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\005\000\007\001\000\000\020\034\357\037\340\375\003\274\177\000\003\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\005\000W\000\200\007\230\\\004\004\007\000\000\000\260\200\003\000G\000\200\007\230\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\007\000\307\004\000\000\020\034\004\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\007\000w\000\200\007\230\\\004\000G\000\200\007\230\\\006\007\367\017\000\200\327[\357\037\340\375\003\274\177\000\007\007\367\017@\002\330[\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\017\031\340\375\003\274\177\000\004\006\007\000\000\000\220\200\007\000\307\001\000\000\020\034\b\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\007\000w\000\200\007\230\\\b\000\207\000\200\007\230\\\006\007\367\017\000\200\327[\357\037\340\375\003\274\177\000\007\007\367\017@\004\330[\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\017\031\340\375\003\274\177\000\b\006\007\000\000\000\220\200\007\000\307\002\000\000\020\034\t\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\007\000w\000\200\007\230\\\t\000\227\000\200\007\230\\\006\007\367\017\000\200\327[\357\037\340\375\003\274\177\000\007\007\367\017\300\004\330[\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\017\031\340!\003\274\177\000\006\006\007\000\000\000\220\200\b\bg\000\000\0038\\\007\004\207\000\000\000\020\\\357\037\340\375\003\274\177\000\t\000\207\002\000\000\020\034\004\002\367\017\000\b\020\\\t\000\227\000\200\007\230\\\357\037\340\375\003\274\177\000\004\000G\000\200\007\230\\\b\t\367\017\000\200\327[\t\t\367\017@\002\330[\357\037\340\375\003<d\000\b\000\207\000\200\007\230\\\t\000\227\000\200\007\230\\\004\b\007\000\000\000\220\200\357\037\340!\003\274\177\000\007\007G\000\000\000\020\\\007:w\000\000\000\340\\\b\007\367\001\000\000)8\357\037\340\375\003\274\177\000\t\000\207\000\200\007\230\\\b\000w\000\200\007\230\\\007\000\207\000\200\007\230\\\357\037\340\375\003\274\177\000\b\000\227\000\200\007\230\\\007\000w\000\200\007\230\\\b\000\207\000\200\007\230\\\357\037\340\375\003\274\177\000\b\007'\000@\004\3706\007\007'\000\000\000H8\003\003w\000\000\200\020\\\357\037\340\375\003\274\177\000\005\005\207\000\000\b\020\\\t\0007\000\200\007\230\\\003\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\b\t\367\017\000\200\327[\t\t\367\017\300\001\330[\b\000\207\000\200\007\230\\\357\037\340!\003<d\000\t\000\227\000\200\007\230\\\003\b\007\000\000\000\220\200\006:g\000\000\000\340\\\357\037\340\375\003\274\177\000\007\006\367\001\000\000)8\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\t\000g\000\200\007\230\\\n\000w\000\200\007\230\\\t\000\227\000\200\007\230\\\357\037\340!\003\274\177\000\n\000\247\000\200\007\230\\\006\n\007@\000\000\3408\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\007\000\367\017\200\007\230\\\005\000g\000\200\007\230\\\b\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\007\000W\000\200\007\230\\\b\000\207\000\200\007\230\\\005\000\007\000\200\007\230L\357\037\340\375\003\274\177\000\006\000\367\017\200\007\230\\\005\007W\000\000\002G\\\006\bg\000\000\002G\\\357\037\340\375\003\274\177\000\b\tg\000@\005\3706\007\tg\000\000\000H8\007\005w\000\000\200\020\\\357\037\340!\003\274\177\000\b\006\207\000\000\b\020\\\004:G\000\000\000\340\\\005\004\367\001\000\000)8\357\037\340\375\003\274\177\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\006\000G\000\200\007\230\\\357\037\340\375\003\274\177\000\004\000W\000\200\007\230\\\005\000g\000\200\007\230\\\006\000G\000\200\007\230\\\357\037\340\375\003\274\177\000\006\005'\000@\003\3706\005\005'\000\000\000H8\005\007W\000\000\200\020\\\357\037\340\375\003\274\177\000\006\bg\000\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340=\003\274g\000\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\000\000\007\000\200\033\250\360\365\007\340\375\003\274\177\000\000\000\007\000\000\000\230\357\006\000\367\017\200\007\230\\\005\000\007\005\000\000\020\034\357\037\340\375\003\274\177\000\003\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\357\037\340=\003\274\177\000\005\000W\000\200\007\230\\\006\004\007\000\000\000\220\240\000\000\000`\000\000\220\342\357\037\340\375\003\274\177\000\017\000\007\000\000\000@\342\005\000\007\005\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340\375\003\274\177\000\004\004\007\000\000\000\220\200\007\004\367\000\200\003i6\017\000\000\000\000\000\370\360\357\037\340\375\003\274\177\000\017\000\007\000\000\000@\342\005\000\307\002\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340!\003\274\177\000\004\004\007\000\000\000\220\200\004:G\000\000\000\340\\\003\004\367\001\000\000)8\357\037\340\375\003\274\177\000\004\000G\000\200\007\230\\\003\0007\000\200\007\230\\\t\004g\000\300\001\3706\357\037\340!\003\274\177\000\006\004g\000\000\000H8\004\n\367\017\000\000\340\\\004\000G\000\200\007\230\\\357\037\340\375\003\274\177\000\005\000\367\017\200\007\230\\\003\000G\000\200\007\230\\\004\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\003\0007\000\200\007\230\\\004\000G\000\200\007\230\\\007\000\007\000\200\007\230L\357\037\340\375\003\274\177\000\b\000\367\017\200\007\230\\\007\003w\000\000\002G\\\b\004\207\000\000\002G\\\357\037\340\375\003\274\177\000\007\007g\000\000\200\020\\\b\b\227\000\000\b\020\\\005\000\007\005\000\000\020\034\357\037\340\375\003\274\177\000\003\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\357\037\340!\003<d\000\005\000W\000\200\007\230\\\004\004\007\000\000\000\220\200\004:G\000\000\000\340\\\357\037\340\375\003\274\177\000\003\004\367\001\000\000)8\006\000G\000\200\007\230\\\t\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\003\006'\000\300\004\3706\005\006'\000\000\000H8\005\007W\000\000\200\020\\\357\037\340\375\003\274\177\000\003\b7\000\000\b\020\\\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\200\t\006g\000\300\004\3706\357\037\340!\003\274\177\000\006\006g\000\000\000H8\004\n\007@\000\000\3408\004\000G\000\200\007\230\\\357\037\340\375\003\274\177\000\005\000\367\017\200\007\230\\\007\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\004\000w\000\200\007\230\\\005\000W\000\200\007\230\\\007\000\007\000\200\007\230L\357\037\340\375\003\274\177\000\b\000\367\017\200\007\230\\\007\004w\000\000\002G\\\b\005\207\000\000\002G\\\357\037\340\375\003\274\177\000\007\007g\000\000\200\020\\\b\b\227\000\000\b\020\\\005\000\207\002\000\000\020\034\357\037\340\375\003\274\177\000\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340!\003<d\000\005\000W\000\200\007\230\\\004\004\007\000\000\000\220\200\004:G\000\000\000\340\\\357\037\340\375\003\274\177\000\005\004\367\001\000\000)8\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\006\004'\000\300\002\3706\005\004'\000\000\000H8\005\007W\000\000\200\020\\\357\037\340\375\003\274\177\000\006\bg\000\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\005\000W\000\200\007\230\\\006\004\007\000\000\000\220\200\005\000G\004\000\000\020\034\357\037\340\375\003\274\177\000\007\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\007\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017\300\003\330[\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\005\000W\000\200\007\230\\\004\004\007\000\000\000\220\200\003\003g\000\000\002\200Y\357\037\340\375\003\274\177\000\005\000G\004\000\000\020\034\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\357\037\340\375\003\274g\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\357\037\340\375\003\274\177\000\017\000\007\000\000\000@\342\005\000\007\005\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340\375\003\274\177\000\004\004\007\000\000\000\220\200\003\004\027\000\000\000\000\034\005\000\007\005\000\000\020\034\357\037\340\375\003\274\177\000\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340=\003\274\177\000\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\017\000\007\241\377\017@\342\357\031\240\376\000\274\177\000\000\000\007\000\200\033\250\360\000\000\007\000\000\000\230\357\017\000\007\000\000\000@\342\357\037\340\375\003\274\177\000\005\000\207\003\000\000\020\034\003\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\005\005\367\017\300\001\330[\357\037\340\375\003<d\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\200\357\037\340\375\003\274\177\000\005\000\207\004\000\000\020\034\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\357\037\340\375\003<d\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\004\004\007\000\000\000\220\200\357\037\340\375\003\274\177\000\003\0047\000\000\000\020\\\005\000\207\004\000\000\020\034\006\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\031\340\375\003\274\177\000\003\004\007\000\000\000\220\240\005\000\007\004\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340\375\003\274\177\000\003\004\007\000\000\000\220\200\005\000\307\004\000\000\020\034\006\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340\375\003\274\177\000\004\004\007\000\000\000\220\200\003\0047\000\000\000\020\\\005\000\307\004\000\000\020\034\357\037\340\375\003\274\177\000\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340=\003\274\177\000\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\240\017\000\207\315\376\017@\342\357\037\340\375\003\274\177\000\005\000\307\001\000\000\020\034\003\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\005\005\367\017\300\001\330[\357\037\340\375\003<d\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\003\004\007\000\000\000\220\200\357\037\340\375\003\274\177\000\005\000G\002\000\000\020\034\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\357\037\340\375\003\274\177\000\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\357\037\340\375\003<d\000\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\004\004\007\000\000\000\220\200\017\031\340\375\003\274\177\000\003\003G\000\000\0038\\\003\003G\000\000\000H8\005\000\007\002\000\000\020\034\357\037\340\375\003\274\177\000\006\002\367\017\000\b\020\\\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\004\005\367\017\000\200\327[\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\357\037\340!\003\274\177\000\005\000W\000\200\007\230\\\004\004\007\000\000\000\220\200\004\004G\000\000\000H8\357\037\340\375\003\274\177\000\003\003G\000\000\000\020\\\005\000G\005\000\000\020\034\006\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\006\000g\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017@\003\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\031\340\375\003\274\177\000\003\004\007\000\000\000\220\240\005\000G\004\000\000\020\034\003\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\005\000W\000\200\007\230\\\003\0007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017\300\001\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\017\031\340\375\003\274\177\000\003\004\007\000\000\000\220\200\007\000\367\017\000\200\020\\\004\002\367\017\000\b\020\\\357\037\340\375\003\274\177\000\007\000w\000\200\007\230\\\004\000G\000\200\007\230\\\006\007\367\017\000\200\327[\357\037\340\375\003\274\177\000\007\007\367\017@\002\330[\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\017\031\340\375\003\274\177\000\006\006\007\000\000\000\260\200\004\000g\000\200\007\230\\\005\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\007\000G\005\000\000\020\034\b\002\367\017\000\b\020\\\007\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\b\000\207\000\200\007\230\\\006\007\367\017\000\200\327[\007\007\367\017@\004\330[\357\037\340\375\003<d\000\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\b\006\007\000\000\000\220\200\357\037\340\375\003\274\177\000\007\000\307\001\000\000\020\034\t\002\367\017\000\b\020\\\007\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\t\000\227\000\200\007\230\\\006\007\367\017\000\200\327[\007\007\367\017\300\004\330[\357\037\340\375\003<d\000\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\t\006\007\000\000\000\220\200\357\037\340\375\003\274\177\000\007\000\307\002\000\000\020\034\n\002\367\017\000\b\020\\\007\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\n\000\247\000\200\007\230\\\006\007\367\017\000\200\327[\007\007\367\017@\005\330[\357\037\340\375\003<d\000\006\000g\000\200\007\230\\\007\000w\000\200\007\230\\\006\006\007\000\000\000\220\200\017\031\340\375\003\274\177\000\006\tg\000\000\0038\\\b\bg\000\000\000\020\\\007\000\207\002\000\000\020\034\357\037\340\375\003\274\177\000\000\002\367\017\000\b\020\\\007\000w\000\200\007\230\\\000\000\007\000\200\007\230\\\357\037\340\375\003\274\177\000\006\007\367\017\000\200\327[\007\007\367\017@\000\330[\006\000g\000\200\007\230\\\357\037\340!\003\274\177\000\007\000w\000\200\007\230\\\006\006\007\000\000\000\220\200\006\bg\000\000\000\020\\\017\031\340\375\003\274\177\000\006:g\000\000\000\340\\\007\006\367\001\000\000)8\006\000g\000\200\007\230\\\357\037\340\375\003\274\177\000\007\000w\000\200\007\230\\\000\000g\000\200\007\230\\\002\000w\000\200\007\230\\\357\037\340\375\003\274\177\000\000\000\007\000\200\007\230\\\002\000'\000\200\007\230\\\002\000'\000@\001\3706\357\037\340\375\003\274\177\000\000\000'\000\000\000H8\004\004\007\000\000\200\020\\\000\005'\000\000\b\020\\\357\037\340\375\003\274\177\000\005\000G\000\200\007\230\\\000\000\007\000\200\007\230\\\004\005\367\017\000\200\327[\357\037\340\375\003\274\177\000\005\005\367\017@\000\330[\004\000G\000\200\007\230\\\005\000W\000\200\007\230\\\357\031\340\375\003\374\037\000\003\004\007\000\000\000\220\240\017\000\007\000\000\000\000\343\017\000\207\377\377\017@\342\340\007\000\374\000\200\037\000\000\017\007\000\000\000\260P\000\017\007\000\000\000\260P\000\017\007\000\000\000\260P\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\001\000\000\000\003\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000@\000\000\000\000\000\000\000\274\001\000\000\000\000\000\000\000\000\000\000\000\000\000\000\001\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\013\000\000\000\003\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\374\001\000\000\000\000\000\000V\003\000\000\000\000\000\000\000\000\000\000\000\000\000\000\001\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\023\000\000\000\002\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000X\005\000\000\000\000\000\0008\001\000\000\000\000\000\000\002\000\000\000\013\000\000\000\b\000\000\000\000\000\000\000\030\000\000\000\000\000\000\000)\000\000\000\000\000\000p\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\220\006\000\000\000\000\000\000`\000\000\000\000\000\000\000\003\000\000\000\000\000\000\000\004\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\\\000\000\000\000\000\000p\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\360\006\000\000\000\000\000\000\264\000\000\000\000\000\000\000\003\000\000\000\n\000\000\000\004\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\037\001\000\000\000\000\000p\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\244\007\000\000\000\000\000\000\264\000\000\000\000\000\000\000\003\000\000\000\013\000\000\000\004\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\255\001\000\000\013\000\000p\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000X\b\000\000\000\000\000\000\340\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\b\000\000\000\000\000\000\000\b\000\000\000\000\000\000\000\303\000\000\000\001\000\000\000\002\000\000\000\000\000\000\000\000\000\000\000\000\000\000\0008\t\000\000\000\000\000\000`\001\000\000\000\000\000\000\000\000\000\000\n\000\000\000\004\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000{\001\000\000\001\000\000\000\002\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\230\n\000\000\000\000\000\000`\001\000\000\000\000\000\000\000\000\000\000\013\000\000\000\004\000\000\000\000\000\000\000\000\000\000\000\000\000\000\0002\000\000\000\001\000\000\000\006\000\020\000\000\000\000\000\000\000\000\000\000\000\000\000\000\f\000\000\000\000\000\000\000%\000\000\000\000\000\000\003\000\000\000\013\000\000\016 \000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\365\000\000\000\001\000\000\000\006\000\020\000\000\000\000\000\000\000\000\000\000\000\000\000\0001\000\000\000\000\000\000\000%\000\000\000\000\000\000\003\000\000\000\f\000\000\016 \000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\211\000\000\000\b\000\000\000\003\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000V\000\000\000\000\000\000\000 \000\000\000\000\000\000\000\000\000\000\n\000\000\000\004\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\270\000\000\000\b\000\000\000\003\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000V\000\000\000\000\000\000\002\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\001\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000L\001\000\000\b\000\000\000\003\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000V\000\000\000\000\000\000\000\b\000\000\000\000\000\000\000\000\000\000\013\000\000\000\004\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\001\000\001\001H\000\000\000\210\b\000\000\000\000\000\000\202\b\000\000@\000\000\000\005\000\007\0002\000\000\000\000\000\000\000\000\000\000\000\021 \000\000\000\000\000\000\000\000\000\000\000\000\000\000\364&\000\000\000\000\000\000\000\000\000\000\000\000\000\000\360 \n\n\n\n.version 7.5\n.target sm_50\n.address_size 64.\000\377\021global .align 1 .b8 blockIdx[1];\"\000\003ethread#\000\304weak .shared)\000\0214)\000\377\036_ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2As[1024K\0001\037BK\000&.32\226\000O4096K\0001\025BK\000\377\003\n.visible .entry _\326\000\017\337(\n.param .u641\000\021\021_/\000?_0,9\000$\03719\000%\02729\000/329\000\030\03739\000%\2464\n)\n{\n.loc\244\002\0228\232\001\021_\025\000\240_depot0[88\311\001\313reg .b64 %SP\017\000\024L\020\000\020p\345\001U%p<3>\"\000v32 %r<6\022\000\020f\022\000If<8>E\000@rd<5$\000c\nmov.uW\000\033,\212\000b;\ncvta\262\000\004%\000\023,\201\000\"ld\006\001\001\005\001o%r2, [\013\001\030\035]A\000\0371A\000\032\0313A\000\002\321\000\0373B\000\032\0372B\000\000\017\305\000\033\0371B\000\000\017\306\000\033#0]b\001#to\274\004\004H\000 4,\006\000\0233\037\000\n\034\000\0215\034\000\0374;\000\005\0216\037\000\0372;\000\002\0217\034\000\0376;\000\005\0218\037\000\0371;\000\002\0219\034\000Q8;\nst\023\000q[%SP+0]\026\000\0329\026\000\0228\026\000\0327\026\000\"16\027\000\0225\027\000\"32\027\000!24\027\000\0331\026\000\002D\000\"2;\240\002\001\"\002\2723, %ctaid.x-\000!32-\000\0303-\000\0254-\000\033y-\000\002\207\000\0304-\000\000R\001\036tX\000\0224\337\000\0305+\000\0236+\000\013V\000\0224\306\000\0216\022\002\002*\000%7,\341\000\007\026\000%8,\207\000\241;\nmul.lo.s\032\000\"9,5\000 %re\001#hl\313\003210,\035\000\0334r\000\002\"\001'10]\000511,\035\000\b\027\000\0302\213\0003addr\000\0201R\001\0015\000\000$\000\t\032\000#4, \000\033-\231\001\0225l\001\0311@\001k15, 16)\000\002h\001(15\211\000%6,\262\001\t\344\000#7,\036\000\013\345\000\0226\202\001(17E\000%8,$\002\tE\000#9,\036\000\fE\000\003b\002\0309\263\000[20, 0(\000\003t\002\bR\001\0312R\001\b.\000\0227\t\001(21.\000%2,\270\000\013.\000\002\016\001\362\00522;\nbra.uni LBB0_1;\n\b\000\027:F\000%3,c\000\b]\000%4,\203\001\222;\nsetp.gt\260\0013p1,8\000\000'\000\242;\n@%p1 brad\000\0338t\000\0232t\000\0222t\000\002\337\003\0241^\000\0308t\000/39\213\000\002/40\334\002\002\0254-\001\0344\335\002342,8\000\000'\000\bk\002343,i\000\000&\000\bN\000%4,\207\003\t1\000%5,7\000\0224\006\005C.s64\036\000\"d1\037\000\0235\033\002\003\336\000$6,\034\000\004\350\002\003\031\000$7,\376\000\000\007\000\002\300\003\0022\007\001\314\001\000\"\000\036]d\000\0248\310\000\001Q\002\003B\001.9,c\t\017\374\005\000\000\256\t\003b\005\002\246\t\004A\000\"20\205\000\0319\273\000\000\202\002\003s\000\0316\273\000\000m\002\0066\000-21\246\000\0252#\001\bO\000$4,\034\000\n\n\001825,V\000\002D\003\021f\300\002\000\035\000\000\277\002\023f\237\001\003\257\000%6,\331\005\b\267\001\004\027\000\0327\027\000\0307\266\003\006\027\000\03783\002\007#9,8\000\000'\000\003\254\000\001\032\000350,i\000\000&\000\007N\000/513\002\003552,7\000\0375)\001\000\001d\000*52)\001$8,\034\000\013)\001$9,\377\000\000\007\000\02783\002\"3,.\001\03693\002$30\310\000\0073\002?31,,\f\026\0173\002\002\0223\256\001)31\273\000433,s\000\n3\002834,6\000\0363\246\000\0245#\001\bO\000$6,\034\000\n\n\001837,V\000\03163\002!373\002\3233;\nbar.sync 0\375\000\002\254\001\0353\201\005\0238p\007\0333\261\004\0233\261\004'3:\353\001\005\260\004\0350\016\005#2,!\000!15\f\005\0262\f\005\0336[\000\0234[\000\0214[\000\002.\002)d3\225\002\b\n\001$9, \000\0316\311\001/40\374\003,\000;\005\001H\000\ta\001\0026\005\004\035\000739;\245\000)43\001\001\006\245\000\0008\005\003 \000\n\342\003\0029\005\005U\000\0274\271\002\0224\271\002+45K\000\0256K\000\013\360\000\0377\271\002,\"48Z\000\0327\242\000(9,\035\000\0306\360\000(50\334\003\007\360\000\000\365\003\003 \000\n\360\000\002\366\003\004U\000'51\360\000\0225\360\000\001J\007\005\025\000%6,\b\b\203;\nfma.rn\032\000\"7,$\001\0019\000(%f\351\002\0058\b+f7[\002\0235[\002&5:\266\002(61\265\001$ad\336\000362,\036\000\0371\023\003\002/62\023\003\004)6:_\003\t$\000\0237$\000\0277\201\000555,\237\t\007\236\005(56\350\007\006\230\000357,\036\000\000;\000\n\233\000\003\001\t\0305\234\t558,t\t\b\027\000\0309\024\006\007\367\000#0,\036\000\000;\000\013_\000\0022\t/602\t\004\0278\326\000/25]\006\002/26m\013\006\002\363\005\0212\022\003(26G\n\002\370\005\000 \000\0304K\000\0379\272\n\003\002\327\005\0222\237\013\006\331\000\000\331\005\004K\000\0343\346\004\002\217\n'31{\002\0301{\002\025l&\003\000\374\013\003h\r\007\214\000\000\277\005\004J\000\b\027\000\0373\005\001\002/34b\007\006\002\356\005\002.\006\0323\300\000#6,i\000\000&\000\bN\000\0377b\007\003\000\023\005\0057\000\0357X\006\002d\f)38\271\003\000\227\f\003\034\000\n\271\003\002\230\f\001\376\000\001'\000\tX\006 13?\001\277f1;\nret;\n\n}\250\022\023/32\250\022\034/32\250\022$\0179\000\003\03719\000%\017\250\022\020\0179\000\003\03739\000\021\017\250\022(\0371\250\022v\0371\250\0220/32\250\022,/32\250\022-/32\250\022-/32\250\022-/32\250\022\377\3770\0138\001\017\250\022}/32\250\0221\0375\250\0221\0375\250\022\225\0231v\t\0371\250\022H\0331\250\022\0231\250\022\0371\250\022\377W/32\250\0224\0377\250\022\377\327/32\250\0224\0377\250\022\257\0231\225\017\0371\250\022\037*31\250\022\0331\250\022\0231\250\022\0371\250\022\037\0367\250\022\017\374\003)\017\250\022\216\0367\250\022\017\271\002)\017\250\022\316\0231\250\022\0371\250\022A\006\023\003\017\250\022\007\0231\250\022\0371\250\022\272\0062\t\017\250\022O\0375\250\022\032\0375\250\022\377cP\n\n}\n\000\000\000\000\000\000\000"
	.size	.L__unnamed_3, 25313

	.type	__cuda_fatbin_wrapper,@object   # @__cuda_fatbin_wrapper
	.section	.nvFatBinSegment,"a",@progbits
	.p2align	3
__cuda_fatbin_wrapper:
	.long	1180844977                      # 0x466243b1
	.long	1                               # 0x1
	.quad	.L__unnamed_3
	.quad	0
	.size	__cuda_fatbin_wrapper, 24

	.type	__cuda_gpubin_handle,@object    # @__cuda_gpubin_handle
	.local	__cuda_gpubin_handle
	.comm	__cuda_gpubin_handle,8,8
	.section	.init_array,"aw",@init_array
	.p2align	3
	.quad	__cuda_module_ctor
	.ident	"clang version 14.0.1"
	.section	".note.GNU-stack","",@progbits
	.addrsig
	.addrsig_sym _Z16checkCmdLineFlagiPPKcS0_
	.addrsig_sym strstr
	.addrsig_sym _Z21getCmdLineArgumentIntiPPKcS0_
	.addrsig_sym atoi
	.addrsig_sym _Z14findCudaDeviceiPPKc
	.addrsig_sym cudaGetDeviceCount
	.addrsig_sym fprintf
	.addrsig_sym cudaGetErrorString
	.addrsig_sym exit
	.addrsig_sym cudaSetDevice
	.addrsig_sym cudaGetDeviceProperties
	.addrsig_sym printf
	.addrsig_sym _Z12ConstantInitPfif
	.addrsig_sym _Z14MatrixMultiplyiPPciRK4dim3S3_
	.addrsig_sym _ZL14cudaMallocHostIfE9cudaErrorPPT_mj
	.addrsig_sym cudaMalloc
	.addrsig_sym cudaEventCreate
	.addrsig_sym cudaStreamCreateWithFlags
	.addrsig_sym cudaMemcpyAsync
	.addrsig_sym __cudaPushCallConfiguration
	.addrsig_sym _Z28__device_stub__MatrixMulCUDAILi16EEvPfS0_S0_ii
	.addrsig_sym _Z28__device_stub__MatrixMulCUDAILi32EEvPfS0_S0_ii
	.addrsig_sym cudaStreamSynchronize
	.addrsig_sym cudaEventRecord
	.addrsig_sym cudaEventSynchronize
	.addrsig_sym cudaEventElapsedTime
	.addrsig_sym cudaFreeHost
	.addrsig_sym cudaFree
	.addrsig_sym cudaEventDestroy
	.addrsig_sym cudaProfilerStart
	.addrsig_sym cudaProfilerStop
	.addrsig_sym _ZL14cudaMallocHostPPvmj
	.addrsig_sym cudaHostAlloc
	.addrsig_sym __cudaPopCallConfiguration
	.addrsig_sym cudaLaunchKernel
	.addrsig_sym __cuda_register_globals
	.addrsig_sym __cudaRegisterFunction
	.addrsig_sym __cudaRegisterFatBinary
	.addrsig_sym __cuda_module_ctor
	.addrsig_sym __cudaRegisterFatBinaryEnd
	.addrsig_sym __cudaUnregisterFatBinary
	.addrsig_sym __cuda_module_dtor
	.addrsig_sym atexit
	.addrsig_sym stderr
	.addrsig_sym .L__unnamed_3
	.addrsig_sym __cuda_fatbin_wrapper
	.addrsig_sym __cuda_gpubin_handle
