; ModuleID = 'matrixMul-cuda-nvptx64-nvidia-cuda-sm_50.bc'
source_filename = "matrixMul.cu"
target datalayout = "e-i64:64-i128:128-v16:16-v32:32-n16:32:64"
target triple = "nvptx64-nvidia-cuda"

%struct.__cuda_builtin_blockIdx_t = type { i8 }
%struct.__cuda_builtin_threadIdx_t = type { i8 }

$_Z13MatrixMulCUDAILi16EEvPfS0_S0_ii = comdat any

$_ZN25__cuda_builtin_blockIdx_t17__fetch_builtin_xEv = comdat any

$_ZN25__cuda_builtin_blockIdx_t17__fetch_builtin_yEv = comdat any

$_ZN26__cuda_builtin_threadIdx_t17__fetch_builtin_xEv = comdat any

$_ZN26__cuda_builtin_threadIdx_t17__fetch_builtin_yEv = comdat any

$_Z13MatrixMulCUDAILi32EEvPfS0_S0_ii = comdat any

$_ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2As = comdat any

$_ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2Bs = comdat any

$_ZZ13MatrixMulCUDAILi32EEvPfS0_S0_iiE2As = comdat any

$_ZZ13MatrixMulCUDAILi32EEvPfS0_S0_iiE2Bs = comdat any

@blockIdx = extern_weak dso_local addrspace(1) global %struct.__cuda_builtin_blockIdx_t, align 1
@threadIdx = extern_weak dso_local addrspace(1) global %struct.__cuda_builtin_threadIdx_t, align 1
@_ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2As = linkonce_odr dso_local addrspace(3) global [16 x [16 x float]] undef, comdat, align 4
@_ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2Bs = linkonce_odr dso_local addrspace(3) global [16 x [16 x float]] undef, comdat, align 4
@_ZZ13MatrixMulCUDAILi32EEvPfS0_S0_iiE2As = linkonce_odr dso_local addrspace(3) global [32 x [32 x float]] undef, comdat, align 4
@_ZZ13MatrixMulCUDAILi32EEvPfS0_S0_iiE2Bs = linkonce_odr dso_local addrspace(3) global [32 x [32 x float]] undef, comdat, align 4

; Function Attrs: convergent mustprogress noinline norecurse nounwind optnone
define dso_local void @_Z13MatrixMulCUDAILi16EEvPfS0_S0_ii(float* noundef %0, float* noundef %1, float* noundef %2, i32 noundef %3, i32 noundef %4) #0 comdat {
  %6 = alloca float*, align 8
  %7 = alloca float*, align 8
  %8 = alloca float*, align 8
  %9 = alloca i32, align 4
  %10 = alloca i32, align 4
  %11 = alloca i32, align 4
  %12 = alloca i32, align 4
  %13 = alloca i32, align 4
  %14 = alloca i32, align 4
  %15 = alloca i32, align 4
  %16 = alloca i32, align 4
  %17 = alloca i32, align 4
  %18 = alloca i32, align 4
  %19 = alloca i32, align 4
  %20 = alloca float, align 4
  %21 = alloca i32, align 4
  %22 = alloca i32, align 4
  %23 = alloca i32, align 4
  %24 = alloca i32, align 4
  store float* %0, float** %6, align 8
  store float* %1, float** %7, align 8
  store float* %2, float** %8, align 8
  store i32 %3, i32* %9, align 4
  store i32 %4, i32* %10, align 4
  %25 = call noundef i32 @_ZN25__cuda_builtin_blockIdx_t17__fetch_builtin_xEv() #2
  store i32 %25, i32* %11, align 4
  %26 = call noundef i32 @_ZN25__cuda_builtin_blockIdx_t17__fetch_builtin_yEv() #2
  store i32 %26, i32* %12, align 4
  %27 = call noundef i32 @_ZN26__cuda_builtin_threadIdx_t17__fetch_builtin_xEv() #2
  store i32 %27, i32* %13, align 4
  %28 = call noundef i32 @_ZN26__cuda_builtin_threadIdx_t17__fetch_builtin_yEv() #2
  store i32 %28, i32* %14, align 4
  %29 = load i32, i32* %9, align 4
  %30 = mul nsw i32 %29, 16
  %31 = load i32, i32* %12, align 4
  %32 = mul nsw i32 %30, %31
  store i32 %32, i32* %15, align 4
  %33 = load i32, i32* %15, align 4
  %34 = load i32, i32* %9, align 4
  %35 = add nsw i32 %33, %34
  %36 = sub nsw i32 %35, 1
  store i32 %36, i32* %16, align 4
  store i32 16, i32* %17, align 4
  %37 = load i32, i32* %11, align 4
  %38 = mul nsw i32 16, %37
  store i32 %38, i32* %18, align 4
  %39 = load i32, i32* %10, align 4
  %40 = mul nsw i32 16, %39
  store i32 %40, i32* %19, align 4
  store float 0.000000e+00, float* %20, align 4
  %41 = load i32, i32* %15, align 4
  store i32 %41, i32* %21, align 4
  %42 = load i32, i32* %18, align 4
  store i32 %42, i32* %22, align 4
  br label %43

43:                                               ; preds = %107, %5
  %44 = load i32, i32* %21, align 4
  %45 = load i32, i32* %16, align 4
  %46 = icmp sle i32 %44, %45
  br i1 %46, label %47, label %114

47:                                               ; preds = %43
  %48 = load float*, float** %7, align 8
  %49 = load i32, i32* %21, align 4
  %50 = load i32, i32* %9, align 4
  %51 = load i32, i32* %14, align 4
  %52 = mul nsw i32 %50, %51
  %53 = add nsw i32 %49, %52
  %54 = load i32, i32* %13, align 4
  %55 = add nsw i32 %53, %54
  %56 = sext i32 %55 to i64
  %57 = getelementptr inbounds float, float* %48, i64 %56
  %58 = load float, float* %57, align 4
  %59 = load i32, i32* %14, align 4
  %60 = sext i32 %59 to i64
  %61 = getelementptr inbounds [16 x [16 x float]], [16 x [16 x float]]* addrspacecast ([16 x [16 x float]] addrspace(3)* @_ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2As to [16 x [16 x float]]*), i64 0, i64 %60
  %62 = load i32, i32* %13, align 4
  %63 = sext i32 %62 to i64
  %64 = getelementptr inbounds [16 x float], [16 x float]* %61, i64 0, i64 %63
  store float %58, float* %64, align 4
  %65 = load float*, float** %8, align 8
  %66 = load i32, i32* %22, align 4
  %67 = load i32, i32* %10, align 4
  %68 = load i32, i32* %14, align 4
  %69 = mul nsw i32 %67, %68
  %70 = add nsw i32 %66, %69
  %71 = load i32, i32* %13, align 4
  %72 = add nsw i32 %70, %71
  %73 = sext i32 %72 to i64
  %74 = getelementptr inbounds float, float* %65, i64 %73
  %75 = load float, float* %74, align 4
  %76 = load i32, i32* %14, align 4
  %77 = sext i32 %76 to i64
  %78 = getelementptr inbounds [16 x [16 x float]], [16 x [16 x float]]* addrspacecast ([16 x [16 x float]] addrspace(3)* @_ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2Bs to [16 x [16 x float]]*), i64 0, i64 %77
  %79 = load i32, i32* %13, align 4
  %80 = sext i32 %79 to i64
  %81 = getelementptr inbounds [16 x float], [16 x float]* %78, i64 0, i64 %80
  store float %75, float* %81, align 4
  call void @llvm.nvvm.barrier0()
  store i32 0, i32* %23, align 4
  br label %82

82:                                               ; preds = %103, %47
  %83 = load i32, i32* %23, align 4
  %84 = icmp slt i32 %83, 16
  br i1 %84, label %85, label %106

85:                                               ; preds = %82
  %86 = load i32, i32* %14, align 4
  %87 = sext i32 %86 to i64
  %88 = getelementptr inbounds [16 x [16 x float]], [16 x [16 x float]]* addrspacecast ([16 x [16 x float]] addrspace(3)* @_ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2As to [16 x [16 x float]]*), i64 0, i64 %87
  %89 = load i32, i32* %23, align 4
  %90 = sext i32 %89 to i64
  %91 = getelementptr inbounds [16 x float], [16 x float]* %88, i64 0, i64 %90
  %92 = load float, float* %91, align 4
  %93 = load i32, i32* %23, align 4
  %94 = sext i32 %93 to i64
  %95 = getelementptr inbounds [16 x [16 x float]], [16 x [16 x float]]* addrspacecast ([16 x [16 x float]] addrspace(3)* @_ZZ13MatrixMulCUDAILi16EEvPfS0_S0_iiE2Bs to [16 x [16 x float]]*), i64 0, i64 %94
  %96 = load i32, i32* %13, align 4
  %97 = sext i32 %96 to i64
  %98 = getelementptr inbounds [16 x float], [16 x float]* %95, i64 0, i64 %97
  %99 = load float, float* %98, align 4
  %100 = fmul contract float %92, %99
  %101 = load float, float* %20, align 4
  %102 = fadd contract float %101, %100
  store float %102, float* %20, align 4
  br label %103

103:                                              ; preds = %85
  %104 = load i32, i32* %23, align 4
  %105 = add nsw i32 %104, 1
  store i32 %105, i32* %23, align 4
  br label %82, !llvm.loop !8

106:                                              ; preds = %82
  call void @llvm.nvvm.barrier0()
  br label %107

107:                                              ; preds = %106
  %108 = load i32, i32* %17, align 4
  %109 = load i32, i32* %21, align 4
  %110 = add nsw i32 %109, %108
  store i32 %110, i32* %21, align 4
  %111 = load i32, i32* %19, align 4
  %112 = load i32, i32* %22, align 4
  %113 = add nsw i32 %112, %111
  store i32 %113, i32* %22, align 4
  br label %43, !llvm.loop !11

114:                                              ; preds = %43
  %115 = load i32, i32* %10, align 4
  %116 = mul nsw i32 %115, 16
  %117 = load i32, i32* %12, align 4
  %118 = mul nsw i32 %116, %117
  %119 = load i32, i32* %11, align 4
  %120 = mul nsw i32 16, %119
  %121 = add nsw i32 %118, %120
  store i32 %121, i32* %24, align 4
  %122 = load float, float* %20, align 4
  %123 = load float*, float** %6, align 8
  %124 = load i32, i32* %24, align 4
  %125 = load i32, i32* %10, align 4
  %126 = load i32, i32* %14, align 4
  %127 = mul nsw i32 %125, %126
  %128 = add nsw i32 %124, %127
  %129 = load i32, i32* %13, align 4
  %130 = add nsw i32 %128, %129
  %131 = sext i32 %130 to i64
  %132 = getelementptr inbounds float, float* %123, i64 %131
  store float %122, float* %132, align 4
  ret void
}

; Function Attrs: alwaysinline convergent mustprogress nounwind
define linkonce_odr dso_local noundef i32 @_ZN25__cuda_builtin_blockIdx_t17__fetch_builtin_xEv() #1 comdat align 2 {
  %1 = call i32 @llvm.nvvm.read.ptx.sreg.ctaid.x()
  ret i32 %1
}

; Function Attrs: alwaysinline convergent mustprogress nounwind
define linkonce_odr dso_local noundef i32 @_ZN25__cuda_builtin_blockIdx_t17__fetch_builtin_yEv() #1 comdat align 2 {
  %1 = call i32 @llvm.nvvm.read.ptx.sreg.ctaid.y()
  ret i32 %1
}

; Function Attrs: alwaysinline convergent mustprogress nounwind
define linkonce_odr dso_local noundef i32 @_ZN26__cuda_builtin_threadIdx_t17__fetch_builtin_xEv() #1 comdat align 2 {
  %1 = call i32 @llvm.nvvm.read.ptx.sreg.tid.x()
  ret i32 %1
}

; Function Attrs: alwaysinline convergent mustprogress nounwind
define linkonce_odr dso_local noundef i32 @_ZN26__cuda_builtin_threadIdx_t17__fetch_builtin_yEv() #1 comdat align 2 {
  %1 = call i32 @llvm.nvvm.read.ptx.sreg.tid.y()
  ret i32 %1
}

; Function Attrs: convergent nounwind
declare void @llvm.nvvm.barrier0() #2

; Function Attrs: nounwind readnone speculatable
declare i32 @llvm.nvvm.read.ptx.sreg.ctaid.x() #3

; Function Attrs: nounwind readnone speculatable
declare i32 @llvm.nvvm.read.ptx.sreg.ctaid.y() #3

; Function Attrs: nounwind readnone speculatable
declare i32 @llvm.nvvm.read.ptx.sreg.tid.x() #3

; Function Attrs: nounwind readnone speculatable
declare i32 @llvm.nvvm.read.ptx.sreg.tid.y() #3

; Function Attrs: convergent mustprogress noinline norecurse nounwind optnone
define dso_local void @_Z13MatrixMulCUDAILi32EEvPfS0_S0_ii(float* noundef %0, float* noundef %1, float* noundef %2, i32 noundef %3, i32 noundef %4) #0 comdat {
  %6 = alloca float*, align 8
  %7 = alloca float*, align 8
  %8 = alloca float*, align 8
  %9 = alloca i32, align 4
  %10 = alloca i32, align 4
  %11 = alloca i32, align 4
  %12 = alloca i32, align 4
  %13 = alloca i32, align 4
  %14 = alloca i32, align 4
  %15 = alloca i32, align 4
  %16 = alloca i32, align 4
  %17 = alloca i32, align 4
  %18 = alloca i32, align 4
  %19 = alloca i32, align 4
  %20 = alloca float, align 4
  %21 = alloca i32, align 4
  %22 = alloca i32, align 4
  %23 = alloca i32, align 4
  %24 = alloca i32, align 4
  store float* %0, float** %6, align 8
  store float* %1, float** %7, align 8
  store float* %2, float** %8, align 8
  store i32 %3, i32* %9, align 4
  store i32 %4, i32* %10, align 4
  %25 = call noundef i32 @_ZN25__cuda_builtin_blockIdx_t17__fetch_builtin_xEv() #2
  store i32 %25, i32* %11, align 4
  %26 = call noundef i32 @_ZN25__cuda_builtin_blockIdx_t17__fetch_builtin_yEv() #2
  store i32 %26, i32* %12, align 4
  %27 = call noundef i32 @_ZN26__cuda_builtin_threadIdx_t17__fetch_builtin_xEv() #2
  store i32 %27, i32* %13, align 4
  %28 = call noundef i32 @_ZN26__cuda_builtin_threadIdx_t17__fetch_builtin_yEv() #2
  store i32 %28, i32* %14, align 4
  %29 = load i32, i32* %9, align 4
  %30 = mul nsw i32 %29, 32
  %31 = load i32, i32* %12, align 4
  %32 = mul nsw i32 %30, %31
  store i32 %32, i32* %15, align 4
  %33 = load i32, i32* %15, align 4
  %34 = load i32, i32* %9, align 4
  %35 = add nsw i32 %33, %34
  %36 = sub nsw i32 %35, 1
  store i32 %36, i32* %16, align 4
  store i32 32, i32* %17, align 4
  %37 = load i32, i32* %11, align 4
  %38 = mul nsw i32 32, %37
  store i32 %38, i32* %18, align 4
  %39 = load i32, i32* %10, align 4
  %40 = mul nsw i32 32, %39
  store i32 %40, i32* %19, align 4
  store float 0.000000e+00, float* %20, align 4
  %41 = load i32, i32* %15, align 4
  store i32 %41, i32* %21, align 4
  %42 = load i32, i32* %18, align 4
  store i32 %42, i32* %22, align 4
  br label %43

43:                                               ; preds = %107, %5
  %44 = load i32, i32* %21, align 4
  %45 = load i32, i32* %16, align 4
  %46 = icmp sle i32 %44, %45
  br i1 %46, label %47, label %114

47:                                               ; preds = %43
  %48 = load float*, float** %7, align 8
  %49 = load i32, i32* %21, align 4
  %50 = load i32, i32* %9, align 4
  %51 = load i32, i32* %14, align 4
  %52 = mul nsw i32 %50, %51
  %53 = add nsw i32 %49, %52
  %54 = load i32, i32* %13, align 4
  %55 = add nsw i32 %53, %54
  %56 = sext i32 %55 to i64
  %57 = getelementptr inbounds float, float* %48, i64 %56
  %58 = load float, float* %57, align 4
  %59 = load i32, i32* %14, align 4
  %60 = sext i32 %59 to i64
  %61 = getelementptr inbounds [32 x [32 x float]], [32 x [32 x float]]* addrspacecast ([32 x [32 x float]] addrspace(3)* @_ZZ13MatrixMulCUDAILi32EEvPfS0_S0_iiE2As to [32 x [32 x float]]*), i64 0, i64 %60
  %62 = load i32, i32* %13, align 4
  %63 = sext i32 %62 to i64
  %64 = getelementptr inbounds [32 x float], [32 x float]* %61, i64 0, i64 %63
  store float %58, float* %64, align 4
  %65 = load float*, float** %8, align 8
  %66 = load i32, i32* %22, align 4
  %67 = load i32, i32* %10, align 4
  %68 = load i32, i32* %14, align 4
  %69 = mul nsw i32 %67, %68
  %70 = add nsw i32 %66, %69
  %71 = load i32, i32* %13, align 4
  %72 = add nsw i32 %70, %71
  %73 = sext i32 %72 to i64
  %74 = getelementptr inbounds float, float* %65, i64 %73
  %75 = load float, float* %74, align 4
  %76 = load i32, i32* %14, align 4
  %77 = sext i32 %76 to i64
  %78 = getelementptr inbounds [32 x [32 x float]], [32 x [32 x float]]* addrspacecast ([32 x [32 x float]] addrspace(3)* @_ZZ13MatrixMulCUDAILi32EEvPfS0_S0_iiE2Bs to [32 x [32 x float]]*), i64 0, i64 %77
  %79 = load i32, i32* %13, align 4
  %80 = sext i32 %79 to i64
  %81 = getelementptr inbounds [32 x float], [32 x float]* %78, i64 0, i64 %80
  store float %75, float* %81, align 4
  call void @llvm.nvvm.barrier0()
  store i32 0, i32* %23, align 4
  br label %82

82:                                               ; preds = %103, %47
  %83 = load i32, i32* %23, align 4
  %84 = icmp slt i32 %83, 32
  br i1 %84, label %85, label %106

85:                                               ; preds = %82
  %86 = load i32, i32* %14, align 4
  %87 = sext i32 %86 to i64
  %88 = getelementptr inbounds [32 x [32 x float]], [32 x [32 x float]]* addrspacecast ([32 x [32 x float]] addrspace(3)* @_ZZ13MatrixMulCUDAILi32EEvPfS0_S0_iiE2As to [32 x [32 x float]]*), i64 0, i64 %87
  %89 = load i32, i32* %23, align 4
  %90 = sext i32 %89 to i64
  %91 = getelementptr inbounds [32 x float], [32 x float]* %88, i64 0, i64 %90
  %92 = load float, float* %91, align 4
  %93 = load i32, i32* %23, align 4
  %94 = sext i32 %93 to i64
  %95 = getelementptr inbounds [32 x [32 x float]], [32 x [32 x float]]* addrspacecast ([32 x [32 x float]] addrspace(3)* @_ZZ13MatrixMulCUDAILi32EEvPfS0_S0_iiE2Bs to [32 x [32 x float]]*), i64 0, i64 %94
  %96 = load i32, i32* %13, align 4
  %97 = sext i32 %96 to i64
  %98 = getelementptr inbounds [32 x float], [32 x float]* %95, i64 0, i64 %97
  %99 = load float, float* %98, align 4
  %100 = fmul contract float %92, %99
  %101 = load float, float* %20, align 4
  %102 = fadd contract float %101, %100
  store float %102, float* %20, align 4
  br label %103

103:                                              ; preds = %85
  %104 = load i32, i32* %23, align 4
  %105 = add nsw i32 %104, 1
  store i32 %105, i32* %23, align 4
  br label %82, !llvm.loop !12

106:                                              ; preds = %82
  call void @llvm.nvvm.barrier0()
  br label %107

107:                                              ; preds = %106
  %108 = load i32, i32* %17, align 4
  %109 = load i32, i32* %21, align 4
  %110 = add nsw i32 %109, %108
  store i32 %110, i32* %21, align 4
  %111 = load i32, i32* %19, align 4
  %112 = load i32, i32* %22, align 4
  %113 = add nsw i32 %112, %111
  store i32 %113, i32* %22, align 4
  br label %43, !llvm.loop !13

114:                                              ; preds = %43
  %115 = load i32, i32* %10, align 4
  %116 = mul nsw i32 %115, 32
  %117 = load i32, i32* %12, align 4
  %118 = mul nsw i32 %116, %117
  %119 = load i32, i32* %11, align 4
  %120 = mul nsw i32 32, %119
  %121 = add nsw i32 %118, %120
  store i32 %121, i32* %24, align 4
  %122 = load float, float* %20, align 4
  %123 = load float*, float** %6, align 8
  %124 = load i32, i32* %24, align 4
  %125 = load i32, i32* %10, align 4
  %126 = load i32, i32* %14, align 4
  %127 = mul nsw i32 %125, %126
  %128 = add nsw i32 %124, %127
  %129 = load i32, i32* %13, align 4
  %130 = add nsw i32 %128, %129
  %131 = sext i32 %130 to i64
  %132 = getelementptr inbounds float, float* %123, i64 %131
  store float %122, float* %132, align 4
  ret void
}

attributes #0 = { convergent mustprogress noinline norecurse nounwind optnone "frame-pointer"="all" "min-legal-vector-width"="0" "no-trapping-math"="true" "stack-protector-buffer-size"="8" "target-cpu"="sm_50" "target-features"="+ptx75,+sm_50" }
attributes #1 = { alwaysinline convergent mustprogress nounwind "frame-pointer"="all" "min-legal-vector-width"="0" "no-trapping-math"="true" "stack-protector-buffer-size"="8" "target-cpu"="sm_50" "target-features"="+ptx75,+sm_50" }
attributes #2 = { convergent nounwind }
attributes #3 = { nounwind readnone speculatable }

!llvm.module.flags = !{!0, !1, !2, !3}
!nvvm.annotations = !{!4, !5}
!llvm.ident = !{!6, !7}

!0 = !{i32 2, !"SDK Version", [2 x i32] [i32 11, i32 5]}
!1 = !{i32 1, !"wchar_size", i32 4}
!2 = !{i32 4, !"nvvm-reflect-ftz", i32 0}
!3 = !{i32 7, !"frame-pointer", i32 2}
!4 = !{void (float*, float*, float*, i32, i32)* @_Z13MatrixMulCUDAILi16EEvPfS0_S0_ii, !"kernel", i32 1}
!5 = !{void (float*, float*, float*, i32, i32)* @_Z13MatrixMulCUDAILi32EEvPfS0_S0_ii, !"kernel", i32 1}
!6 = !{!"clang version 14.0.1"}
!7 = !{!"clang version 3.8.0 (tags/RELEASE_380/final)"}
!8 = distinct !{!8, !9, !10}
!9 = !{!"llvm.loop.mustprogress"}
!10 = !{!"llvm.loop.unroll.enable"}
!11 = distinct !{!11, !9}
!12 = distinct !{!12, !9, !10}
!13 = distinct !{!13, !9}
