#ifndef LLVM_TO_CPP_CONVERTER_H
#define LLVM_TO_CPP_CONVERTER_H

#include "llvm/IR/Module.h"
#include "llvm/IR/Function.h"
#include "llvm/IR/BasicBlock.h"
#include "llvm/IR/Instruction.h"
#include "llvm/IR/Instructions.h"
#include "llvm/IR/Constants.h"
#include "llvm/IR/Type.h"
#include "llvm/IR/Value.h"
#include "llvm/IR/IntrinsicInst.h"
#include "llvm/IR/Intrinsics.h"
#include "llvm/IR/DerivedTypes.h"
#include "llvm/IR/GetElementPtrTypeIterator.h"
#include "llvm/Support/raw_ostream.h"
#include "llvm/Support/Casting.h"
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <set>
#include <vector>
#include <sstream>
#include <fstream>
#include <map>
#include "CudaSimulation.h"

namespace llvm_to_cpp {

class CustomLLVMToCppConverter {
public:
    CustomLLVMToCppConverter(llvm::Module* M);
    ~CustomLLVMToCppConverter();
    
    // Main conversion function
    void convertToC(const std::string& outputPath);
    
private:
    // Type conversion methods
    std::string getCppType(llvm::Type* type, bool isSigned = false);
    std::string convertPointerType(llvm::PointerType* ptrType);
    std::string convertArrayType(llvm::ArrayType* arrayType);
    std::string convertStructType(llvm::StructType* structType);
    std::string convertVectorType(llvm::VectorType* vectorType);
    std::string convertFunctionType(llvm::FunctionType* funcType);
    std::string convertIntegerType(llvm::IntegerType* intType, bool isSigned = false);
    std::string convertFloatingPointType(llvm::Type* fpType);
    
    // Value conversion methods
    std::string convertValue(llvm::Value* value);
    std::string convertConstant(llvm::Constant* constant);
    std::string convertConstantInt(llvm::ConstantInt* ci);
    std::string convertConstantFP(llvm::ConstantFP* cfp);
    std::string convertConstantArray(llvm::ConstantArray* ca);
    std::string convertConstantStruct(llvm::ConstantStruct* cs);
    std::string convertConstantVector(llvm::ConstantVector* cv);
    std::string convertConstantExpr(llvm::ConstantExpr* ce);
    std::string convertGlobalVariable(llvm::GlobalVariable* gv);
    
    // Instruction conversion methods
    std::string convertInstruction(llvm::Instruction* inst);
    std::string convertAllocaInst(llvm::AllocaInst* inst);
    std::string convertLoadInst(llvm::LoadInst* inst);
    std::string convertStoreInst(llvm::StoreInst* inst);
    std::string convertBinaryOp(llvm::BinaryOperator* inst);
    std::string convertUnaryOperator(llvm::UnaryOperator* inst);
    std::string convertICmpInst(llvm::ICmpInst* inst);
    std::string convertFCmpInst(llvm::FCmpInst* inst);
    std::string convertBranchInst(llvm::BranchInst* inst);
    std::string convertReturnInst(llvm::ReturnInst* inst);
    std::string convertCallInst(llvm::CallInst* inst);
    std::string convertGetElementPtrInst(llvm::GetElementPtrInst* inst);
    std::string convertCastInst(llvm::CastInst* inst);
    std::string convertPHINode(llvm::PHINode* inst);
    std::string convertSelectInst(llvm::SelectInst* inst);
    std::string convertSwitchInst(llvm::SwitchInst* inst);
    std::string convertExtractElementInst(llvm::ExtractElementInst* inst);
    std::string convertInsertElementInst(llvm::InsertElementInst* inst);
    std::string convertShuffleVectorInst(llvm::ShuffleVectorInst* inst);
    std::string convertExtractValueInst(llvm::ExtractValueInst* inst);
    std::string convertInsertValueInst(llvm::InsertValueInst* inst);
    
    // Intrinsic and builtin handling
    std::string convertIntrinsicCall(llvm::CallInst* inst);
    std::string convertCudaBuiltin(const std::string& funcName);
    std::string convertMathIntrinsic(llvm::CallInst* inst);
    std::string convertMemoryIntrinsic(llvm::CallInst* inst);
    bool isCudaKernel(llvm::Function* F);
    bool isCudaBuiltin(llvm::Function* func);
    bool isIntrinsicCall(llvm::CallInst* inst);
    std::string getCudaBuiltinName(const std::string& llvmName);
    
    // Operand context for proper casting
    enum OperandContext {
        ContextNormal,
        ContextCasted,
        ContextStatic
    };
    
    std::string writeOperand(llvm::Value* operand, OperandContext context = ContextNormal);
    std::string writeOperandWithCast(llvm::Value* operand, unsigned opcode);
    std::string writeOperandDeref(llvm::Value* operand);
    
    // Type checking and utilities
    bool isEmptyType(llvm::Type* type);
    bool isSignedType(llvm::Type* type);
    bool needsCast(unsigned opcode, bool& shouldCast, bool& castIsSigned);
    std::string getCastString(llvm::Instruction::CastOps opcode, llvm::Type* srcTy, llvm::Type* dstTy);
    
    // Memory access helpers
    std::string writeMemoryAccess(llvm::Value* operand, llvm::Type* operandType, 
                                  bool isVolatile = false, unsigned alignment = 0);
    
    // GEP (GetElementPtr) helpers
    std::string printGEPExpression(llvm::Value* ptr, llvm::gep_type_iterator begin, 
                                   llvm::gep_type_iterator end);
    
    // Control flow helpers
    std::string generateLabel(llvm::BasicBlock* bb);
    std::string generateGoto(llvm::BasicBlock* bb);
    std::string generatePHICopies(llvm::BasicBlock* from, llvm::BasicBlock* to);
    bool isGotoCodeNecessary(llvm::BasicBlock* from, llvm::BasicBlock* to);
    
    // Utility methods
    std::string getValueName(llvm::Value* value);
    std::string generateUniqueName(const std::string& baseName);
    std::string sanitizeName(const std::string& name);
    std::string generateTempVar();
    std::string getIndentation(int level = -1);
    std::string escapeString(const std::string& str);
    
    // Comparison predicates
    std::string getICmpPredicateString(llvm::ICmpInst::Predicate pred);
    std::string getFCmpPredicateString(llvm::FCmpInst::Predicate pred);
    
    // Binary operator strings
    std::string getBinaryOperatorString(llvm::Instruction::BinaryOps op);
    
    // Function and basic block conversion
    void convertFunction(llvm::Function* F, std::ofstream& out);
    void convertGlobalVariables(std::ofstream& out);
    std::string convertBasicBlock(llvm::BasicBlock* bb);
    
    // Variable optimization methods
    std::string getOptimizedVariableName(llvm::Value* value, const std::string& baseType);
    void releaseVariable(const std::string& varName, const std::string& type);
    bool canReuseVariable(const std::string& type, llvm::Value* value);
    void optimizeVariableUsage(llvm::Function* func);
    void analyzeVariableLifetime(llvm::Function* func);

    // Stack usage optimization
    void optimizeStackUsage(llvm::Function* func);
    bool shouldUseHeapAllocation(llvm::Type* type);
    std::string generateHeapAllocation(llvm::Type* type, const std::string& varName);

    // Member variables
    llvm::Module* module;
    std::unordered_map<llvm::Value*, std::string> valueNames;
    std::unordered_set<std::string> usedNames;
    std::unordered_set<llvm::Type*> declaredTypes;
    std::set<llvm::StructType*> structTypesToDeclare;
    std::set<llvm::ArrayType*> arrayTypesToDeclare;

    // Variable optimization tracking
    std::unordered_map<std::string, std::vector<std::string>> availableVariables;  // type -> available vars
    std::unordered_map<std::string, std::string> variableTypes;  // var name -> type
    std::unordered_map<llvm::Value*, std::pair<int, int>> variableLifetime;  // value -> (start, end)
    std::unordered_set<std::string> heapAllocatedVars;  // variables allocated on heap
    std::set<llvm::VectorType*> vectorTypesToDeclare;
    std::set<llvm::FunctionType*> functionTypesToDeclare;
    
    // Anonymous struct name mapping
    std::unordered_map<llvm::StructType*, std::string> anonymousStructNames;
    
    // CUDA function usage tracking
    std::set<std::string> usedCudaBuiltins;
    
    // String constant mapping for proper printf handling
    std::map<llvm::GlobalVariable*, std::string> stringConstants;
    
    // Alloca value tracking for proper pointer semantics
    std::set<llvm::Value*> allocaValues;
    
    int tempVarCounter;
    int labelCounter;
    int indentLevel;
    
    // Current context
    llvm::Function* currentFunction;
    llvm::BasicBlock* currentBasicBlock;
    
    // Code generation helpers
    std::string generateHeaders();
    std::string generateTypeDeclarations();
    std::string generateStructDeclarations();
    std::string generateArrayDeclarations();
    std::string generateArrayDeclarations(bool basicTypesOnly);
    bool isBasicType(llvm::Type* type);
    std::string generateVectorDeclarations();
    std::string generateFunctionTypeDeclarations();
    std::string generateGlobalDeclarations();
    std::string generateFunctionDeclarations();
    std::string generateHelperFunctions();
    std::string generateIntrinsicHelpers();
    std::string generateCudaHelpers();
    
    // Forward declaration collection
    void collectTypes();
    void collectType(llvm::Type* type);
    
    // Comprehensive struct analysis system
    void analyzeAllStructTypes();
    void collectAllStructTypes();
    void collectStructTypesFromType(llvm::Type* type);
    void collectStructTypesFromTypeImpl(llvm::Type* type, std::set<llvm::Type*>& visiting);
    void collectStructTypesFromValue(llvm::Value* value);
    void analyzeStructDependencies();
    void generateStructDefinitions();
    std::string generateAllStructDeclarations();
    std::vector<llvm::StructType*> topologicalSortStructs();
    bool hasStructDependency(llvm::StructType* from, llvm::StructType* to);
    
    // Enhanced struct tracking
    std::set<llvm::StructType*> allStructTypes;
    std::unordered_map<llvm::StructType*, std::set<llvm::StructType*>> structDependencies;
    std::unordered_map<llvm::StructType*, std::string> structDefinitions;
    
    // CUDA builtin analysis
    void analyzeCudaBuiltinUsage();
    void analyzeFunctionForCudaBuiltins(llvm::Function* F);
    
    // Variable declaration helpers
    std::string generateLocalVariables(llvm::Function* function);
    bool needsLocalDeclaration(llvm::Instruction* inst);
    
    // PHI node handling
    std::unordered_map<llvm::PHINode*, std::string> phiTempVars;
    void generatePHIAssignments(llvm::BasicBlock* bb, std::ofstream& out);
    
    // Fat Binary and GEP handling
    std::string extractFatbinContent(llvm::GlobalVariable* gv);
    std::string generateGEPExpression(llvm::ConstantExpr* ce);
    std::string generateTypedGEPExpression(llvm::ConstantExpr* ce);
    std::string readFatbinFile(const std::string& filename);
    std::string parseFatbinData(const std::string& data);
    
    // String constant handling
    std::string extractStringFromConstantArray(llvm::ConstantArray* ca);
    void preprocessStringConstants();
    std::string findKernelNameInStringConstants(const std::string& kernelSuffix);
    std::string extractKernelNameFromArgument(llvm::Value* kernelArg);
};

} // namespace llvm_to_cpp

#endif // LLVM_TO_CPP_CONVERTER_H